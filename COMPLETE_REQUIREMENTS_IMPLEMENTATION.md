# 完整需求实现说明

## 需求总览

✅ **需求1**: 挂空单、多单时，第一列都会展示k+挂单数
✅ **需求2**: 部分成交后每次页面刷新，第一列都会动态展示k+未成交数
✅ **需求3**: 全部成交后，第一列k会消失
✅ **需求4**: 上侧空单区第四列一直展示当前价位总未成交数
✅ **需求5**: 下侧多单区第二列一直展示当前价位总未成交数

## 实现细节

### 需求1: 挂单时显示k+挂单数

**实现位置**: `TradingPanel.vue` - `handleOrderStatusUpdate`函数

```javascript
} else if (status === 51 || status === 97) {
  // 需求1: 挂空单、多单时，第一列都会展示k+挂单数
  const statusText = status === 51 ? '未成交还在队列中' : '未知状态（刚提交）'
  
  // 添加或更新订单的未成交数量
  priceTableRef.value.addPendingOrder(orderType, roundedPrice, orderRef.toString(), remainingVolume)
  
  // 同时更新市场深度数据
  updateMarketDepthWithOrder(orderType, roundedPrice, remainingVolume, true)
}
```

**触发时机**: 
- 状态97（未知状态，刚提交）
- 状态51（未成交还在队列中）

### 需求2: 页面刷新时动态展示k+未成交数

**实现位置**: `TradingPanel.vue` - `loadCurrentPendingOrders`函数

```javascript
// 页面加载时获取当前未成交订单 - 需求2: 页面刷新时动态展示k+未成交数
const loadCurrentPendingOrders = async () => {
  // 查询所有订单状态
  const result = await invoke('query_all_orders', { sessionId: traderSessionId })
  
  for (const order of result.data) {
    const remainingVolume = order.volume - order.volume_traded
    
    if (order.order_status === '未成交还在队列中' && remainingVolume > 0) {
      // 恢复k+数字显示（显示实际未成交数量）
      priceTableRef.value.addPendingOrder(orderType, price, orderRef, remainingVolume)
    }
  }
}
```

**触发时机**: 页面加载时（延迟2秒执行）

### 需求3: 全部成交后k消失

**实现位置**: `TradingPanel.vue` - `handleOrderStatusUpdate`函数

```javascript
if (status === 48 || status === 53) {
  // 需求3: 全部成交或撤单后，第一列k会消失
  const statusText = status === 48 ? '全部成交' : '已撤单'
  
  priceTableRef.value.removePendingOrder(orderType, roundedPrice, orderRef.toString(), volume)
  
  // 同时更新市场深度数据
  updateMarketDepthWithOrder(orderType, roundedPrice, volume, false)
}
```

**触发时机**:
- 状态48（全部成交）
- 状态53（撤单）

### 需求4&5: 第二列和第四列显示总未成交数

**实现位置**: `PriceTable.vue` - 模板和计算函数

#### 计算函数:
```javascript
// 需求4&5: 获取包含市场深度和未成交订单的总数量
const getTotalBuyVolume = (price: number): number => {
  // 市场深度数量 + 我的未成交订单数量
  const marketVolume = props.priceOrders.find(item => item.price === price)?.buyVolume || 0
  const myPendingVolume = getBuyOrderCount(price)
  return marketVolume + myPendingVolume
}

const getTotalSellVolume = (price: number): number => {
  // 市场深度数量 + 我的未成交订单数量
  const marketVolume = props.priceOrders.find(item => item.price === price)?.sellVolume || 0
  const myPendingVolume = getSellOrderCount(price)
  return marketVolume + myPendingVolume
}

// 格式化数量显示（如果有我的未成交订单，高亮显示）
const formatVolumeDisplay = (totalVolume: number, myVolume: number): string => {
  if (totalVolume === 0) return ''
  if (myVolume > 0) {
    // 如果有我的未成交订单，显示格式：总量(我的量)
    return `${totalVolume}(${myVolume})`
  }
  return totalVolume.toString()
}
```

#### 模板更新:
```vue
<!-- 第二列：买量 - 需求5: 下侧多单区第二列展示当前价位总未成交数 -->
<div class="buy-volume-col clickable"
  :class="{ 'has-my-orders': getBuyOrderCount(item.price) > 0 }">
  {{ formatVolumeDisplay(getTotalBuyVolume(item.price), getBuyOrderCount(item.price)) }}
</div>

<!-- 第四列：卖量 - 需求4: 上侧空单区第四列展示当前价位总未成交数 -->
<div class="sell-volume-col clickable"
  :class="{ 'has-my-orders': getSellOrderCount(item.price) > 0 }">
  {{ formatVolumeDisplay(getTotalSellVolume(item.price), getSellOrderCount(item.price)) }}
</div>
```

#### 高亮样式:
```css
/* 有我的订单时的高亮样式 - 需求4&5 */
.buy-volume-col.has-my-orders {
  background-color: rgba(255, 107, 107, 0.15) !important;
  color: #ff6b6b !important;
  font-weight: bold !important;
  border: 1px solid rgba(255, 107, 107, 0.3) !important;
}

.sell-volume-col.has-my-orders {
  background-color: rgba(81, 207, 102, 0.15) !important;
  color: #51cf66 !important;
  font-weight: bold !important;
  border: 1px solid rgba(81, 207, 102, 0.3) !important;
}
```

## 数据结构优化

### 新的未成交订单数据结构
```javascript
// 修改前：简单累加
Map<price, {refs: string[], quantity: number}>

// 修改后：按订单引用独立存储
Map<price, Map<orderRef, quantity>>
```

### 优势
1. **精确跟踪**: 每个订单引用对应独立的未成交数量
2. **部分成交处理**: 支持精确的部分成交数量更新
3. **多订单支持**: 同一价格多个订单正确累加显示

## 实时性保证

### 1. 事件驱动更新
- 订单状态变化时立即更新k+数字
- 同步更新市场深度数据
- 实时反映未成交数量变化

### 2. 页面刷新恢复
- 页面加载时查询CTP获取真实状态
- 恢复所有未成交订单的k+数字显示
- 确保页面刷新后数据准确

### 3. 备用机制
- F9键手动触发状态恢复
- 自动检测并补充遗漏的订单
- 多重保障确保数据一致性

## 显示效果

### 第一列 k+数字
- **挂单时**: k20（显示挂单数量）
- **部分成交**: k15（显示剩余未成交数量）
- **全部成交**: 无显示（k+数字消失）

### 第二列（买量）和第四列（卖量）
- **无我的订单**: 显示市场深度数量，如 "50"
- **有我的订单**: 显示总量(我的量)，如 "70(20)"，并高亮显示
- **只有我的订单**: 显示我的数量，如 "20"

## 测试验证

### 测试场景1: 基本挂单
1. 挂买单20手 → 第一列显示k20，第二列高亮显示包含我的20手
2. 挂卖单15手 → 第一列显示k15，第四列高亮显示包含我的15手

### 测试场景2: 部分成交
1. 买单20手成交5手 → 第一列更新为k15
2. 第二列数量相应减少5手

### 测试场景3: 全部成交
1. 剩余15手全部成交 → 第一列k+数字消失
2. 第二列恢复正常显示（无高亮）

### 测试场景4: 页面刷新
1. 刷新页面 → 所有k+数字正确恢复到实际未成交数量
2. 第二列和第四列正确显示总未成交数

## 调试工具

- **F9**: 手动恢复k+数字状态
- **F10**: 测试订单状态更新
- **F11**: 手动刷新k+数字显示
- **F12**: 查看详细k+数字状态
