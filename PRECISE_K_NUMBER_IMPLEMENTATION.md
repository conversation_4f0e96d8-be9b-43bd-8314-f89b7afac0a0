# 精确k+数字实现方案

## 🎯 需求分析

您的需求：
> 在每次推送时，后台能获取到当前已挂单数的被成交量吗？用于展示 k+未成交 = k +(挂单数-已成交数) ，当挂单数已全部成交后，k+数字在下次刷新时消失掉

## 📊 当前数据基础

### 后端已有数据
```rust
pub struct OrderInfo {
    pub order_ref: String,        // 订单引用
    pub volume: i32,              // 原始挂单数
    pub volume_traded: i32,       // 已成交数
    pub order_status: String,     // 订单状态
    // ... 其他字段
}
```

### 计算公式
```
k+未成交数 = 挂单数 - 已成交数
remainingVolume = order.volume - order.volume_traded
```

## 🔄 实现方案

### 方案：市场数据推送时同步订单状态

**核心思路**：
1. 每次收到市场数据推送时
2. 同步查询当前所有订单状态
3. 重新计算每个订单的未成交数量
4. 更新k+数字显示

### 技术实现

#### 1. 事件监听器增强
```javascript
// 监听市场数据更新
marketDataUnlisten = await listen('market_data', (event) => {
  try {
    // 更新市场数据
    updateMarketData(event.payload)
    
    // 同步检查订单状态并更新k+数字
    syncOrderStatusOnMarketUpdate()
    
  } catch (error) {
    console.error('市场数据更新失败:', error)
  }
})
```

#### 2. 订单状态同步函数
```javascript
const syncOrderStatusOnMarketUpdate = async () => {
  // 防止频繁调用
  if (syncOrderStatusOnMarketUpdate.isRunning) return
  syncOrderStatusOnMarketUpdate.isRunning = true
  
  try {
    // 查询所有订单状态
    const result = await invoke('query_all_orders', { sessionId })
    
    for (const order of result.data) {
      // 计算剩余未成交数量
      const remainingVolume = order.volume - order.volume_traded
      
      if (remainingVolume > 0) {
        // 更新k+数字为实际剩余数量
        priceTableRef.value.addPendingOrder(orderType, price, orderRef, remainingVolume)
      } else {
        // 全部成交，移除k+数字
        priceTableRef.value.removePendingOrder(orderType, price, orderRef, 0)
      }
    }
  } finally {
    syncOrderStatusOnMarketUpdate.isRunning = false
  }
}
```

## 📈 实际场景演示

### 场景1：部分成交过程

**初始状态**：
```
挂单：买入20手 @ 3214
显示：k20
```

**第一次成交5手**：
```
市场数据推送 → 同步订单状态
查询结果：volume=20, volume_traded=5
计算：remainingVolume = 20 - 5 = 15
更新显示：k15
```

**第二次成交10手**：
```
市场数据推送 → 同步订单状态
查询结果：volume=20, volume_traded=15
计算：remainingVolume = 20 - 15 = 5
更新显示：k5
```

**全部成交**：
```
市场数据推送 → 同步订单状态
查询结果：volume=20, volume_traded=20
计算：remainingVolume = 20 - 20 = 0
更新显示：k+数字消失
```

### 场景2：多个订单同时管理

**初始状态**：
```
订单A：买入10手 @ 3214 → k10
订单B：买入15手 @ 3213 → k15
```

**部分成交后**：
```
订单A：成交3手，剩余7手 → k7
订单B：成交0手，剩余15手 → k15
```

**全部成交后**：
```
订单A：成交10手，剩余0手 → 消失
订单B：成交15手，剩余0手 → 消失
```

## 🔧 技术特点

### 1. 实时同步
- 每次市场数据更新都会同步订单状态
- 确保k+数字始终反映最新的未成交数量

### 2. 精确计算
- 直接从CTP获取准确的成交数据
- 避免本地计算误差

### 3. 自动清理
- 全部成交的订单自动移除k+数字
- 无需手动干预

### 4. 性能优化
- 使用节流机制防止频繁调用
- 只在数量变化时更新显示

## 📊 数据流程图

```
市场数据推送
    ↓
更新五档行情
    ↓
同步查询订单状态
    ↓
计算剩余未成交数量
    ↓
更新k+数字显示
    ↓
如果全部成交则移除k+数字
```

## 🎯 预期效果

### 显示准确性
- k+数字始终显示实际未成交数量
- 不会出现数量不匹配的情况

### 实时性
- 随市场数据推送实时更新
- 成交后立即反映在k+数字上

### 自动化
- 全部成交后自动消失
- 无需手动管理订单状态

## 🔍 调试和监控

### 关键日志
```
🔄 [Frontend] 市场数据更新时同步订单状态...
📊 [Frontend] 同步到订单数据: X个订单
🔄 [Frontend] 同步更新订单 123:
   - 价格: 3214
   - 原始挂单数: 20
   - 已成交数: 5
   - k+未成交数: 15
✅ [Frontend] 订单 456 已全部成交，k+数字消失
```

### 性能监控
- 同步频率：跟随市场数据推送
- 节流保护：防止重复调用
- 错误处理：确保异常不影响主流程

## 🚀 优势总结

1. **数据准确**：直接从CTP获取成交数据
2. **实时更新**：跟随市场数据推送
3. **自动管理**：全部成交后自动清理
4. **性能优化**：节流机制防止过度调用
5. **错误恢复**：异常情况下不影响主功能

这个实现方案完全满足您的需求：**k+未成交 = k+(挂单数-已成交数)**，并且**当挂单数已全部成交后，k+数字在下次刷新时消失掉**。
