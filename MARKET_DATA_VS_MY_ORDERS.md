# 五档行情数据 vs 我的挂单数据

## 当前实现（数据分离显示）

### 显示方式
```
价格档位表格：
第一列：k+数字（我的挂单）  |  第二列：买量（市场数据）  |  第三列：价格  |  第四列：卖量（市场数据）
```

### 示例场景
**假设买一价3214现有市场挂单100手，我挂单40手买入：**

```
k40  |  100  |  3214  |  0
```

- **第一列**: k40（我的40手挂单）
- **第二列**: 100（市场上所有人的买单总量，不包括我的）
- **第四列**: 0（该价位没有卖单）

### 数据来源
1. **第一列k+数字**: 来自我的订单状态更新事件
2. **第二列买量**: 来自CTP五档行情推送的`bid_volume1-5`
3. **第四列卖量**: 来自CTP五档行情推送的`ask_volume1-5`

## 可选实现（合并显示）

### 显示方式
```
价格档位表格：
第一列：k+数字（我的挂单）  |  第二列：买量（市场+我的）  |  第三列：价格  |  第四列：卖量（市场+我的）
```

### 示例场景
**同样场景下：**

```
k40  |  140  |  3214  |  0
```

- **第一列**: k40（我的40手挂单）
- **第二列**: 140（市场100手 + 我的40手）
- **第四列**: 0（该价位没有卖单）

## 两种方案的对比

### 方案1：数据分离（当前实现）

**优点**:
- **数据清晰**: 市场数据和个人数据分开显示
- **符合交易习惯**: 大多数交易软件都是这样设计
- **数据准确**: 市场数据不受个人订单影响
- **便于分析**: 可以清楚看到市场真实的买卖力量

**缺点**:
- **需要心算**: 要知道总挂单量需要自己计算
- **信息分散**: 总量信息不够直观

### 方案2：合并显示

**优点**:
- **信息直观**: 直接显示该价位的总挂单量
- **便于决策**: 一眼就能看到包含我的订单后的总量

**缺点**:
- **数据混淆**: 市场数据和个人数据混合，不够清晰
- **可能误导**: 可能误以为市场上真的有那么多挂单
- **不符合习惯**: 与主流交易软件的显示方式不同

## 技术实现

### 当前实现（方案1）
```javascript
const orderData = {
  price: item.price,
  buyVolume: showBuyVolume ? (marketData?.bidVolume || 0) : 0,  // 只显示市场数据
  sellVolume: showSellVolume ? (marketData?.askVolume || 0) : 0, // 只显示市场数据
}
```

### 合并显示实现（方案2）
```javascript
// 需要获取我的挂单数据
const myBuyVolume = getBuyOrderCount(item.price)  // 从PriceTable获取
const mySellVolume = getSellOrderCount(item.price) // 从PriceTable获取

const orderData = {
  price: item.price,
  buyVolume: showBuyVolume ? ((marketData?.bidVolume || 0) + myBuyVolume) : 0,
  sellVolume: showSellVolume ? ((marketData?.askVolume || 0) + mySellVolume) : 0,
}
```

## 实际交易软件的做法

### 主流期货软件（如文华财经、博易大师等）
- **采用方案1**: 市场数据和个人订单分开显示
- **五档行情**: 显示市场真实的买卖挂单量
- **个人订单**: 单独区域或特殊标记显示

### 股票软件（如同花顺、东方财富等）
- **也采用方案1**: 五档行情显示市场数据
- **个人委托**: 在委托列表中单独显示

## 建议

### 推荐使用方案1（当前实现）

**理由**:
1. **行业标准**: 符合期货交易软件的标准做法
2. **数据准确**: 保持市场数据的真实性
3. **便于分析**: 交易员可以准确判断市场买卖力量
4. **避免混淆**: 不会误导用户对市场深度的判断

### 如果需要总量信息

可以考虑以下方案：
1. **鼠标悬停提示**: 显示"市场量+我的量=总量"
2. **颜色区分**: 我的挂单部分用不同颜色高亮
3. **额外列**: 增加一列显示"总量"

## 当前状态

**当前实现使用方案1（数据分离）**，这是最符合期货交易习惯的做法。

如果您希望改为方案2（合并显示），我可以修改代码实现，但建议保持当前的方案1实现。
