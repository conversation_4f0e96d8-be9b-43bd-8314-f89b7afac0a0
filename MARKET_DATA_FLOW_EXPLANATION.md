# 五档行情数据刷新流程详解

## 整体数据流程

```
CTP后端 → market_data事件 → TradingPanel监听器 → useMarketData.updateMarketData → PriceTable显示
```

## 详细流程分析

### 1. 数据源：CTP后端推送

CTP后端通过WebSocket推送market_data事件，包含完整的五档行情数据：

```json
{
  "instrument_id": "rb2509",
  "last_price": 3215.0,
  "bid_price1": 3214.0,    // 买一价
  "bid_volume1": 50,       // 买一量
  "bid_price2": 3213.0,    // 买二价
  "bid_volume2": 30,       // 买二量
  "bid_price3": 3212.0,    // 买三价
  "bid_volume3": 20,       // 买三量
  "bid_price4": 3211.0,    // 买四价
  "bid_volume4": 15,       // 买四量
  "bid_price5": 3210.0,    // 买五价
  "bid_volume5": 10,       // 买五量
  "ask_price1": 3215.0,    // 卖一价
  "ask_volume1": 40,       // 卖一量
  "ask_price2": 3216.0,    // 卖二价
  "ask_volume2": 35,       // 卖二量
  "ask_price3": 3217.0,    // 卖三价
  "ask_volume3": 25,       // 卖三量
  "ask_price4": 3218.0,    // 卖四价
  "ask_volume4": 20,       // 卖四量
  "ask_price5": 3219.0,    // 卖五价
  "ask_volume5": 15,       // 卖五量
  "volume": 12345,         // 成交量
  "turnover": 39876543.21  // 成交额
}
```

### 2. 前端事件监听：TradingPanel.vue

```javascript
// 监听市场数据更新
marketDataUnlisten = await listen('market_data', (event) => {
  console.log('📊 [Frontend] ========== 收到市场数据更新事件 ==========')
  console.log('📊 [Frontend] 事件数据:', event.payload)
  
  try {
    // 调用useMarketData中的updateMarketData函数
    updateMarketData(event.payload)
    console.log('✅ [Frontend] 市场数据更新成功')
  } catch (error) {
    console.error('❌ [Frontend] 市场数据更新失败:', error)
  }
})
```

### 3. 数据处理：useMarketData.js

#### 3.1 updateMarketData函数
```javascript
const updateMarketData = (data) => {
  console.log('📊 更新行情数据:', data.instrument_id)
  
  if (data && data.instrument_id) {
    // 更新5档买盘数据
    for (let i = 1; i <= 5; i++) {
      const bidPriceKey = `bid_price${i}`
      const bidVolumeKey = `bid_volume${i}`
      
      if (data[bidPriceKey] && data[bidPriceKey] > 0) {
        const bidPrice = Math.round(data[bidPriceKey])
        const existingData = marketDataMap.value.get(bidPrice) || { bidVolume: 0, askVolume: 0 }
        marketDataMap.value.set(bidPrice, {
          bidVolume: data[bidVolumeKey] || 0,
          askVolume: existingData.askVolume
        })
      }
    }

    // 更新5档卖盘数据
    for (let i = 1; i <= 5; i++) {
      const askPriceKey = `ask_price${i}`
      const askVolumeKey = `ask_volume${i}`
      
      if (data[askPriceKey] && data[askPriceKey] > 0) {
        const askPrice = Math.round(data[askPriceKey])
        const existingData = marketDataMap.value.get(askPrice) || { bidVolume: 0, askVolume: 0 }
        marketDataMap.value.set(askPrice, {
          bidVolume: existingData.bidVolume,
          askVolume: data[askVolumeKey] || 0
        })
      }
    }

    // 生成价格档位数据
    generatePriceOrders(data, data.bid_price1, data.ask_price1)
  }
}
```

#### 3.2 generatePriceOrders函数
```javascript
const generatePriceOrders = (data, bidPrice1, askPrice1) => {
  // 1. 根据买一价和卖一价计算价格范围
  const roundedBidPrice1 = Math.round(bidPrice1)
  const roundedAskPrice1 = Math.round(askPrice1)
  
  // 2. 生成完整的价格序列
  const allPrices = []
  
  // 3. 生成卖盘价格：从卖一价往上
  // 4. 补全买一价和卖一价之间的间隔
  // 5. 生成买盘价格：从买一价往下
  
  // 6. 为每个价格添加对应的买量和卖量
  allPrices.forEach((item, index) => {
    const marketData = marketDataMap.value.get(item.price)
    
    const orderData = {
      price: item.price,
      buyVolume: showBuyVolume ? (marketData?.bidVolume || 0) : 0,
      sellVolume: showSellVolume ? (marketData?.askVolume || 0) : 0,
      level: (index + 1).toString(),
      color: item.color
    }
    
    // 分类到卖盘、买盘或间隔区域
  })
  
  // 7. 更新响应式数据
  sellOrders.value = newSellOrders
  buyOrders.value = newBuyOrders
  gapOrders.value = gapOrders
}
```

### 4. 数据展示：PriceTable.vue

```vue
<template>
  <div class="price-table-container">
    <!-- 卖盘区域 -->
    <div class="sell-section">
      <div v-for="(item, index) in sellOrders" :key="`sell-${item.price}`">
        <div class="buy-volume-col">{{ item.buyVolume || '' }}</div>
        <div class="price-col">{{ item.price }}</div>
        <div class="sell-volume-col">{{ item.sellVolume || '' }}</div>
      </div>
    </div>
    
    <!-- 买盘区域 -->
    <div class="buy-section">
      <div v-for="(item, index) in buyOrders" :key="`buy-${item.price}`">
        <div class="buy-volume-col">{{ item.buyVolume || '' }}</div>
        <div class="price-col">{{ item.price }}</div>
        <div class="sell-volume-col">{{ item.sellVolume || '' }}</div>
      </div>
    </div>
  </div>
</template>
```

## 数据映射关系

### 五档数据映射
```
CTP五档数据 → marketDataMap → 价格档位显示

买一价3214，买一量50 → marketDataMap.set(3214, {bidVolume: 50, askVolume: 0})
买二价3213，买二量30 → marketDataMap.set(3213, {bidVolume: 30, askVolume: 0})
...
卖一价3215，卖一量40 → marketDataMap.set(3215, {bidVolume: 0, askVolume: 40})
卖二价3216，卖二量35 → marketDataMap.set(3216, {bidVolume: 0, askVolume: 35})
...
```

### 显示规则
```
价格档位显示规则：
- 买量：只在买一价及以下区域显示
- 卖量：只在卖一价及以上区域显示
- 间隔区域：买一价和卖一价之间，不显示买量和卖量
```

## 实时更新机制

### 1. 数据推送频率
- CTP后端：实时推送，通常每秒多次
- 前端更新：响应式数据，自动触发界面重新渲染

### 2. 性能优化
```javascript
// 防止重复更新
if (isUpdatingOrders) {
  console.log('⚠️ 正在更新档位数据，跳过重复生成')
  return
}
isUpdatingOrders = true
```

### 3. 数据一致性
- 使用Map结构存储价格-数量映射
- 每次更新都会重新生成完整的价格档位
- 确保显示数据与最新行情数据一致

## 调试和监控

### 查看数据流
1. **控制台日志**: 观察market_data事件接收
2. **数据验证**: 检查marketDataMap内容
3. **界面更新**: 确认价格档位正确显示

### 常见问题
1. **数据不更新**: 检查事件监听器是否正常注册
2. **显示错误**: 检查数据映射和显示规则
3. **性能问题**: 检查更新频率和防重复机制

## 总结

五档行情数据的刷新是一个完整的数据流：
1. **CTP推送** → 2. **事件监听** → 3. **数据处理** → 4. **界面更新**

每个环节都有相应的日志和错误处理，确保数据能够实时、准确地显示在交易界面上。
