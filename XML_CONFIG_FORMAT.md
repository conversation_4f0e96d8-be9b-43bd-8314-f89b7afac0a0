# 交易面板XML配置格式说明

## 新格式说明

系统现在支持按照您指定的XML格式保存和加载交易面板配置。

### XML格式结构

```xml
<?xml version="1.0" encoding="UTF-8"?>
<a>
    <a93015007 Title="hc2601" X="1497" Y="151" Width="204" Height="815" RowHeight="16" FZ="2" Z_NUM="2" Y_NUM="20" MS="A" SC="1" />
    <a93015007 Title="si2510" X="3005" Y="167" Width="204" Height="814" RowHeight="16" FZ="1" Z_NUM="1" Y_NUM="10" MS="B" SC="1"/>
    <!-- 更多面板配置... -->
</a>
```

### 属性说明

- **a标签**: 表示一个交易集
- **a93015007**: 表示账号（93015007为账号编号）
- **Title**: 合约代码（如 hc2601, si2510 等）
- **X, Y**: 合约窗口位置坐标
- **Width, Height**: 合约窗口的宽度和高度
- **RowHeight**: 表格行高
- **FZ**: 金手指设置值
- **Z_NUM**: 左键轻仓下单数量
- **Y_NUM**: 右键重仓下单数量  
- **MS**: 多模式(A)还是单模式(B)
- **SC**: 合约在第几个屏幕上

### 功能特性

1. **自动保存**: 系统会自动按照新格式保存配置
2. **兼容性**: 同时支持新格式和旧格式的配置文件
3. **合约信息**: 自动保存和恢复面板的合约代码
4. **窗口状态**: 保存窗口位置、大小等信息
5. **交易设置**: 保存金手指、轻仓重仓数量等交易参数
6. **智能屏幕检测**: 自动检测窗口所在的屏幕编号
   - 使用Tauri API获取真实的监视器信息
   - 根据窗口位置准确判断所在屏幕
   - 支持多屏幕环境和不同分辨率
   - 提供后备计算方法确保兼容性

### 使用方法

1. **保存配置**: 在交易控制器中右键选择"保存配置"
2. **加载配置**: 在交易控制器中右键选择"加载配置"
3. **自动加载**: 系统启动时会自动加载默认配置文件

### 配置文件位置

- 默认配置文件: `td_config.xml`
- 保存位置: 应用程序根目录

### 屏幕检测说明

系统会自动检测每个交易面板窗口所在的屏幕：

1. **多屏幕支持**:
   - 自动获取系统中所有可用的监视器信息
   - 根据窗口坐标准确判断所在屏幕
   - 支持不同分辨率和排列方式的多屏幕设置

2. **检测逻辑**:
   - 优先使用Tauri API的 `monitorFromPoint()` 函数
   - 获取所有监视器列表并找到匹配的屏幕索引
   - 屏幕编号从1开始（SC=1表示第一个屏幕）

3. **后备方案**:
   - 如果API调用失败，使用简单的坐标计算
   - 假设屏幕从左到右排列，每屏1920px宽度
   - 确保在任何情况下都能得到有效的屏幕编号

### 注意事项

- 配置文件会自动覆盖同名文件
- 建议定期备份重要的配置文件
- 新格式配置文件更加简洁，便于手动编辑
- 屏幕编号基于系统监视器顺序，重新排列显示器可能影响编号
