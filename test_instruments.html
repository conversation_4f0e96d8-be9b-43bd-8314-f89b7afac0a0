<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合约查询测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            border: 1px solid #d9d9d9;
        }
        .instrument-item {
            display: flex;
            gap: 15px;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .instrument-id {
            min-width: 80px;
            font-weight: bold;
            color: #1890ff;
        }
        .instrument-name {
            flex: 1;
        }
        .instrument-exchange {
            min-width: 60px;
            color: #666;
        }
        .error {
            color: #ff4d4f;
            background: #fff2f0;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ffccc7;
        }
        .success {
            color: #52c41a;
            background: #f6ffed;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #b7eb8f;
        }
    </style>
</head>
<body>
    <h1>合约查询测试页面</h1>
    
    <div class="container">
        <h3>测试合约查询接口</h3>
        <button id="testBtn" onclick="testInstruments()">查询合约列表</button>
        <button onclick="clearResults()">清除结果</button>
        
        <div id="result" class="result" style="display: none;">
            <div id="status"></div>
            <div id="instruments"></div>
        </div>
    </div>

    <script>
        async function testInstruments() {
            const btn = document.getElementById('testBtn');
            const result = document.getElementById('result');
            const status = document.getElementById('status');
            const instruments = document.getElementById('instruments');
            
            btn.disabled = true;
            btn.textContent = '查询中...';
            result.style.display = 'block';
            status.innerHTML = '<div>正在查询合约列表...</div>';
            instruments.innerHTML = '';
            
            try {
                // 模拟调用 Tauri 命令
                const response = await window.__TAURI__.invoke('query_instruments', {
                    sessionId: 'test_session_' + Date.now()
                });
                
                if (response.success && response.data) {
                    status.innerHTML = `<div class="success">查询成功！共获取到 ${response.data.length} 个合约</div>`;
                    
                    // 按交易所分组统计
                    const exchangeStats = {};
                    response.data.forEach(inst => {
                        const exchange = inst.exchange_id;
                        exchangeStats[exchange] = (exchangeStats[exchange] || 0) + 1;
                    });
                    
                    // 显示统计信息
                    let statsHtml = '<h4>交易所统计：</h4><ul>';
                    Object.entries(exchangeStats).forEach(([exchange, count]) => {
                        statsHtml += `<li>${exchange}: ${count} 个合约</li>`;
                    });
                    statsHtml += '</ul>';
                    
                    // 显示前10个合约
                    let instrumentsHtml = '<h4>合约列表（前10个）：</h4>';
                    response.data.slice(0, 10).forEach(inst => {
                        instrumentsHtml += `
                            <div class="instrument-item">
                                <span class="instrument-id">${inst.instrument_id}</span>
                                <span class="instrument-name">${inst.instrument_name}</span>
                                <span class="instrument-exchange">${inst.exchange_id}</span>
                            </div>
                        `;
                    });
                    
                    if (response.data.length > 10) {
                        instrumentsHtml += `<div style="text-align: center; color: #999; margin-top: 10px;">... 还有 ${response.data.length - 10} 个合约</div>`;
                    }
                    
                    instruments.innerHTML = statsHtml + instrumentsHtml;
                } else {
                    status.innerHTML = `<div class="error">查询失败：${response.error || '未知错误'}</div>`;
                }
            } catch (error) {
                status.innerHTML = `<div class="error">查询异常：${error.message}</div>`;
                console.error('查询合约异常:', error);
            } finally {
                btn.disabled = false;
                btn.textContent = '查询合约列表';
            }
        }
        
        function clearResults() {
            const result = document.getElementById('result');
            result.style.display = 'none';
        }
        
        // 检查 Tauri API 是否可用
        window.addEventListener('DOMContentLoaded', () => {
            if (!window.__TAURI__) {
                document.getElementById('result').style.display = 'block';
                document.getElementById('status').innerHTML = '<div class="error">Tauri API 不可用，请在 Tauri 应用中打开此页面</div>';
            }
        });
    </script>
</body>
</html>
