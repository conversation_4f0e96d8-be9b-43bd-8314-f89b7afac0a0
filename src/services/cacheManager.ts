import { invoke } from '@tauri-apps/api/core';

/**
 * 缓存管理服务
 * 专门用于清理当前目录下的 CTP 缓存
 */

// 类型定义
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * 缓存管理器类
 * 专门用于清理当前目录下的 CTP 缓存
 */
export class CacheManager {
  private static instance: CacheManager;

  private constructor() {}

  // 获取单例实例
  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * 清理当前目录下的 CTP 缓存（temp/ctp_cache）
   */
  async cleanupCurrentDirCache(): Promise<ApiResponse<string>> {
    try {
      console.log('🧹 [CACHE] 开始清理当前目录下的CTP缓存');

      const result = await invoke('cleanup_current_dir_cache_command') as ApiResponse<string>;

      if (result.success) {
        console.log('✅ [CACHE] 当前目录缓存清理成功');
        console.log(`📊 [CACHE] ${result.data}`);
      } else {
        console.error(`❌ [CACHE] 当前目录缓存清理失败: ${result.error}`);
      }

      return result;
    } catch (error) {
      const errorMessage = `清理当前目录缓存异常: ${error}`;
      console.error(`❌ [CACHE] ${errorMessage}`);
      return {
        success: false,
        error: errorMessage
      };
    }
  }


}

// 创建单例实例并导出
export const cacheManager = CacheManager.getInstance();

// 导出便捷函数
export const cleanupCurrentDirCache = () => cacheManager.cleanupCurrentDirCache();
