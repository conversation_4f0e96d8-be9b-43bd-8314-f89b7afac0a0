/**
 * 标题栏组件相关类型定义
 */

/**
 * 标题栏主题配置
 */
export interface TitleBarTheme {
  /** 背景颜色 */
  backgroundColor: string
  /** 文字颜色 */
  textColor: string
  /** 高度 */
  height: string
  /** 边框颜色 */
  borderColor?: string
  /** 悬停时的背景颜色 */
  hoverColor?: string
  /** 激活时的背景颜色 */
  activeColor?: string
}

/**
 * 预定义主题
 */
export const TitleBarThemes: Record<string, TitleBarTheme> = {
  dark: {
    backgroundColor: '#2c2c2c',
    textColor: '#ffffff',
    height: '32px',
    borderColor: 'rgba(255, 255, 255, 0.1)',
    hoverColor: 'rgba(255, 255, 255, 0.1)',
    activeColor: 'rgba(255, 255, 255, 0.2)'
  },
  light: {
    backgroundColor: '#f5f5f5',
    textColor: '#333333',
    height: '32px',
    borderColor: 'rgba(0, 0, 0, 0.1)',
    hoverColor: 'rgba(0, 0, 0, 0.05)',
    activeColor: 'rgba(0, 0, 0, 0.1)'
  },
  primary: {
    backgroundColor: '#1890ff',
    textColor: '#ffffff',
    height: '32px',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    hoverColor: 'rgba(255, 255, 255, 0.1)',
    activeColor: 'rgba(255, 255, 255, 0.2)'
  },
  success: {
    backgroundColor: '#52c41a',
    textColor: '#ffffff',
    height: '32px',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    hoverColor: 'rgba(255, 255, 255, 0.1)',
    activeColor: 'rgba(255, 255, 255, 0.2)'
  },
  warning: {
    backgroundColor: '#faad14',
    textColor: '#ffffff',
    height: '32px',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    hoverColor: 'rgba(255, 255, 255, 0.1)',
    activeColor: 'rgba(255, 255, 255, 0.2)'
  },
  danger: {
    backgroundColor: '#ff4d4f',
    textColor: '#ffffff',
    height: '32px',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    hoverColor: 'rgba(255, 255, 255, 0.1)',
    activeColor: 'rgba(255, 255, 255, 0.2)'
  }
}

/**
 * 标题栏配置选项
 */
export interface TitleBarOptions {
  /** 标题文本 */
  title?: string
  /** 是否显示图标 */
  showIcon?: boolean
  /** 图标源地址 */
  iconSrc?: string
  /** 是否显示窗口控制按钮 */
  showControls?: boolean
  /** 最小化按钮提示文本 */
  minimizeTitle?: string
  /** 最大化按钮提示文本 */
  maximizeTitle?: string
  /** 还原按钮提示文本 */
  restoreTitle?: string
  /** 关闭按钮提示文本 */
  closeTitle?: string
  /** 主题名称或自定义主题 */
  theme?: keyof typeof TitleBarThemes | TitleBarTheme
  /** 是否启用拖拽 */
  draggable?: boolean
  /** 自定义CSS类名 */
  className?: string
}

/**
 * 标题栏事件类型
 */
export interface TitleBarEvents {
  /** 最小化事件 */
  minimize: () => void
  /** 最大化事件 */
  maximize: () => void
  /** 还原事件 */
  restore: () => void
  /** 关闭事件 */
  close: () => void
  /** 双击事件 */
  doubleClick?: () => void
  /** 右键菜单事件 */
  contextMenu?: (event: MouseEvent) => void
}

/**
 * 窗口状态
 */
export interface WindowState {
  /** 是否最大化 */
  isMaximized: boolean
  /** 是否最小化 */
  isMinimized: boolean
  /** 是否全屏 */
  isFullscreen: boolean
  /** 是否可见 */
  isVisible: boolean
  /** 是否聚焦 */
  isFocused: boolean
}

/**
 * 自定义按钮配置
 */
export interface CustomButton {
  /** 按钮ID */
  id: string
  /** 按钮图标 */
  icon?: string
  /** 按钮文本 */
  text?: string
  /** 提示文本 */
  tooltip?: string
  /** 点击事件 */
  onClick: () => void
  /** 是否禁用 */
  disabled?: boolean
  /** 是否显示 */
  visible?: boolean
  /** 自定义样式 */
  style?: Record<string, string>
  /** 自定义CSS类名 */
  className?: string
}

/**
 * 标题栏布局配置
 */
export interface TitleBarLayout {
  /** 左侧区域配置 */
  left?: {
    /** 是否显示图标 */
    showIcon?: boolean
    /** 是否显示标题 */
    showTitle?: boolean
    /** 自定义内容 */
    customContent?: string
  }
  /** 中间区域配置 */
  center?: {
    /** 自定义内容 */
    customContent?: string
    /** 对齐方式 */
    align?: 'left' | 'center' | 'right'
  }
  /** 右侧区域配置 */
  right?: {
    /** 自定义按钮 */
    customButtons?: CustomButton[]
    /** 是否显示窗口控制按钮 */
    showControls?: boolean
  }
}

/**
 * 标题栏组件实例方法
 */
export interface TitleBarInstance {
  /** 更新标题 */
  updateTitle: (title: string) => void
  /** 更新主题 */
  updateTheme: (theme: keyof typeof TitleBarThemes | TitleBarTheme) => void
  /** 获取窗口状态 */
  getWindowState: () => Promise<WindowState>
  /** 刷新窗口状态 */
  refreshWindowState: () => Promise<void>
  /** 添加自定义按钮 */
  addCustomButton: (button: CustomButton) => void
  /** 移除自定义按钮 */
  removeCustomButton: (buttonId: string) => void
  /** 更新自定义按钮 */
  updateCustomButton: (buttonId: string, updates: Partial<CustomButton>) => void
}

/**
 * 标题栏工具函数类型
 */
export interface TitleBarUtils {
  /** 应用主题 */
  applyTheme: (element: HTMLElement, theme: TitleBarTheme) => void
  /** 获取主题 */
  getTheme: (themeName: keyof typeof TitleBarThemes) => TitleBarTheme
  /** 创建自定义主题 */
  createCustomTheme: (baseTheme: keyof typeof TitleBarThemes, overrides: Partial<TitleBarTheme>) => TitleBarTheme
  /** 验证主题配置 */
  validateTheme: (theme: TitleBarTheme) => boolean
}
