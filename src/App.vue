<template>
  <StyleProvider>
    <router-view></router-view>
  </StyleProvider>
</template>
<script setup lang="ts">
import { StyleProvider } from "ant-design-vue";
import { onMounted, onUnmounted } from 'vue';
import { getCurrentWindow } from '@tauri-apps/api/window';
import { useTradingPanels } from '@/composables/trading-controller/useTradingPanels';
import { useSoundManager } from '@/composables/useSoundManager';
import { ctpService } from '@/services/ctpService';
// import { cleanupCurrentDirCache } from '@/services/cacheManager'; // 临时屏蔽缓存管理器

// 使用交易面板管理组合式函数
const { closeAllTradingPanels } = useTradingPanels();

// 使用声音管理器（在应用级别初始化）
const { initSoundManager } = useSoundManager();

let unlistenCloseRequested: (() => void) | null = null;

// 监听主窗口关闭事件
onMounted(async () => {
  try {
    // 初始化声音管理器
    initSoundManager();
    console.log('声音管理器已初始化');

    const mainWindow = getCurrentWindow();

    // 监听窗口关闭请求事件
    unlistenCloseRequested = await mainWindow.onCloseRequested(async (event) => {
      console.log('主窗口关闭请求，开始执行清理流程...');

      try {
        // 1. 关闭所有交易面板
        await closeAllTradingPanels();
        console.log('✅ 所有交易面板已关闭');

        // 2. 清理CTP服务资源
        await ctpService.cleanup();
        console.log('✅ CTP服务资源已清理');

        // 3. 清理缓存目录 - 临时屏蔽
        // await cleanupCurrentDirCache();
        // console.log('✅ 缓存目录已清理');

      } catch (error) {
        console.error('❌ 程序退出清理过程中出错:', error);
      }

      console.log('🎯 程序退出清理流程完成');
      // 允许主窗口关闭
      // 注意：不需要调用 event.preventDefault()，让窗口正常关闭
    });

    console.log('主窗口关闭事件监听器已设置');
  } catch (error) {
    console.error('设置主窗口关闭监听器失败:', error);
  }
});

// 组件卸载时清理监听器
onUnmounted(() => {
  // if (unlistenCloseRequested) {
  //   unlistenCloseRequested();
  //   unlistenCloseRequested = null;
  // }
});
</script>

<style>
html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
}

div,
span,
p {
  margin: 0;
  padding: 0;
}

#app {
  height: 100%;
  width: 100%;
}
</style>
