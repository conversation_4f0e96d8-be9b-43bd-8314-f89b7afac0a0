import { ctpService } from '@/services/ctpService'

/**
 * 持仓数据测试工具
 */
export class PositionTest {
  constructor() {
    this.ctpService = ctpService
  }

  /**
   * 测试持仓数据查询
   */
  async testPositionQuery() {
    console.log('🧪 [PositionTest] ========== 开始测试持仓数据查询 ==========')
    
    try {
      console.log('🔍 [PositionTest] 调用 ctpService.queryPosition()...')
      
      const result = await this.ctpService.queryPosition()
      
      if (result.success) {
        const positions = result.data || []
        console.log('✅ [PositionTest] 持仓查询成功，共', positions.length, '个持仓')
        
        // 分析持仓数据
        let longTotal = 0
        let shortTotal = 0
        
        positions.forEach((pos, index) => {
          const direction = pos.posi_direction === '2' ? '多头' : '空头'
          const quantity = pos.position
          
          console.log(`📊 [PositionTest] 持仓${index + 1}:`, {
            合约: pos.instrument_id,
            方向: direction,
            数量: quantity,
            盈亏: pos.position_profit
          })
          
          if (pos.posi_direction === '2') {
            longTotal += quantity
          } else if (pos.posi_direction === '3') {
            shortTotal += quantity
          }
        })
        
        console.log('📊 [PositionTest] 持仓汇总:')
        console.log(`   - 多头总计: ${longTotal}手 (蓝色显示)`)
        console.log(`   - 空头总计: ${shortTotal}手 (红色显示为 -${shortTotal})`)
        console.log(`   - 净持仓: ${longTotal - shortTotal}手`)
        
        return {
          success: true,
          longPosition: longTotal,
          shortPosition: -shortTotal, // 转为负数
          netPosition: longTotal - shortTotal,
          positions
        }
      } else {
        console.error('❌ [PositionTest] 持仓查询失败:', result.error)
        return { success: false, error: result.error }
      }
    } catch (error) {
      console.error('❌ [PositionTest] 持仓查询异常:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 模拟成交事件测试
   */
  async testTradeNotification() {
    console.log('🧪 [PositionTest] ========== 开始测试成交事件处理 ==========')
    
    // 模拟成交数据
    const mockTradeData = {
      order_ref: 'test_123',
      instrument_id: 'rb2509',
      price: 3500,
      volume: 2,
      direction: '0', // 0=买入, 1=卖出
      trade_id: 'trade_456',
      trade_time: '14:30:25'
    }
    
    console.log('📤 [PositionTest] 模拟成交数据:', mockTradeData)
    
    // 查询成交前的持仓
    console.log('🔍 [PositionTest] 查询成交前持仓...')
    const beforeResult = await this.testPositionQuery()
    
    if (beforeResult.success) {
      console.log('📊 [PositionTest] 成交前持仓:', {
        多头: beforeResult.longPosition,
        空头: beforeResult.shortPosition,
        净持仓: beforeResult.netPosition
      })
    }
    
    // 模拟延迟后查询持仓（实际场景中成交会改变持仓）
    console.log('⏱️ [PositionTest] 等待500ms模拟成交处理...')
    await new Promise(resolve => setTimeout(resolve, 500))
    
    console.log('🔍 [PositionTest] 查询成交后持仓...')
    const afterResult = await this.testPositionQuery()
    
    if (afterResult.success) {
      console.log('📊 [PositionTest] 成交后持仓:', {
        多头: afterResult.longPosition,
        空头: afterResult.shortPosition,
        净持仓: afterResult.netPosition
      })
      
      // 计算变化
      if (beforeResult.success) {
        const longChange = afterResult.longPosition - beforeResult.longPosition
        const shortChange = afterResult.shortPosition - beforeResult.shortPosition
        
        console.log('📈 [PositionTest] 持仓变化:')
        console.log(`   - 多头变化: ${longChange > 0 ? '+' : ''}${longChange}手`)
        console.log(`   - 空头变化: ${shortChange > 0 ? '+' : ''}${shortChange}手`)
      }
    }
    
    console.log('✅ [PositionTest] 成交事件测试完成')
  }

  /**
   * 运行完整测试
   */
  async runFullTest() {
    console.log('🧪 [PositionTest] ========== 开始完整持仓功能测试 ==========')
    
    // 测试1: 基础持仓查询
    await this.testPositionQuery()
    
    console.log('')
    
    // 测试2: 成交事件处理
    await this.testTradeNotification()
    
    console.log('🎉 [PositionTest] ========== 完整测试结束 ==========')
  }
}

// 导出测试函数
export const runPositionTest = async () => {
  const tester = new PositionTest()
  await tester.runFullTest()
}

export const testPositionQuery = async () => {
  const tester = new PositionTest()
  return await tester.testPositionQuery()
}

export const testTradeNotification = async () => {
  const tester = new PositionTest()
  await tester.testTradeNotification()
}
