// 行情数据工具函数
import { contractService } from '@/services/contractService'
import { ctpService } from '@/services/ctpService'

/**
 * 订阅单个合约的行情数据
 * @param contractCode 合约代码
 * @returns 是否订阅成功
 */
export async function subscribeContractMarketData(contractCode: string): Promise<boolean> {
  try {
    console.log(`🔍 开始订阅合约行情: ${contractCode}`)

    // 检查行情 API 是否已连接
    const mdSessionId = ctpService.getMdSessionId()
    if (!mdSessionId) {
      console.warn('📡 行情 API 未连接，无法订阅行情')
      return false
    }

    // 使用合约服务订阅行情
    const success = await contractService.subscribeContractMarketData(contractCode)

    if (success) {
      console.log(`✅ 成功订阅合约 ${contractCode} 的行情数据`)
      return true
    } else {
      console.warn(`⚠️ 订阅合约 ${contractCode} 行情失败`)
      return false
    }
  } catch (error) {
    console.error(`❌ 订阅合约 ${contractCode} 行情异常:`, error)
    console.error(`订阅行情异常: ${error instanceof Error ? error.message : '未知错误'}`)
    return false
  }
}

/**
 * 取消订阅单个合约的行情数据
 * @param contractCode 合约代码
 * @returns 是否取消订阅成功
 */
export async function unsubscribeContractMarketData(contractCode: string): Promise<boolean> {
  try {
    console.log(`🔍 开始取消订阅合约行情: ${contractCode}`)

    // 检查行情 API 是否已连接
    const mdSessionId = ctpService.getMdSessionId()
    if (!mdSessionId) {
      console.warn('📡 行情 API 未连接，无法取消订阅')
      return false
    }

    // 使用合约服务取消订阅行情
    const success = await contractService.unsubscribeContractMarketData(contractCode)
    
    if (success) {
      console.log(`✅ 成功取消订阅合约 ${contractCode} 的行情数据`)
      return true
    } else {
      console.warn(`⚠️ 取消订阅合约 ${contractCode} 行情失败`)
      return false
    }
  } catch (error) {
    console.error(`❌ 取消订阅合约 ${contractCode} 行情异常:`, error)
    return false
  }
}

/**
 * 切换合约行情订阅
 * @param oldContractCode 旧合约代码（如果有）
 * @param newContractCode 新合约代码
 * @returns 是否切换成功
 */
export async function switchContractMarketData(
  oldContractCode: string | null, 
  newContractCode: string
): Promise<boolean> {
  try {
    console.log(`🔄 切换合约行情订阅: ${oldContractCode || '无'} -> ${newContractCode}`)

    // 如果有旧合约，先取消订阅
    if (oldContractCode && oldContractCode !== newContractCode) {
      await unsubscribeContractMarketData(oldContractCode)
    }

    // 订阅新合约
    const success = await subscribeContractMarketData(newContractCode)
    
    if (success) {
      console.log(`✅ 成功切换到合约 ${newContractCode} 的行情`)
      return true
    } else {
      console.warn(`⚠️ 切换到合约 ${newContractCode} 行情失败`)
      return false
    }
  } catch (error) {
    console.error(`❌ 切换合约行情异常:`, error)
    return false
  }
}

/**
 * 批量订阅多个合约的行情数据（谨慎使用）
 * @param contractCodes 合约代码数组
 * @param maxCount 最大订阅数量，默认10个
 * @returns 成功订阅的合约数量
 */
export async function batchSubscribeMarketData(
  contractCodes: string[], 
  maxCount: number = 10
): Promise<number> {
  try {
    console.log(`🔍 批量订阅合约行情，最多 ${maxCount} 个:`, contractCodes.slice(0, 5))

    // 限制订阅数量，避免过多订阅
    const limitedCodes = contractCodes.slice(0, maxCount)
    let successCount = 0

    for (const contractCode of limitedCodes) {
      const success = await subscribeContractMarketData(contractCode)
      if (success) {
        successCount++
      }
      
      // 添加延迟，避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    console.log(`✅ 批量订阅完成，成功订阅 ${successCount}/${limitedCodes.length} 个合约`)
    return successCount
  } catch (error) {
    console.error('❌ 批量订阅合约行情异常:', error)
    return 0
  }
}
