/**
 * CTP 连接诊断工具
 * 用于检查 CTP 连接状态和合约数据获取问题
 */

import { ctpService } from '@/services/ctpService'
import { autoReconnectService } from '@/services/autoReconnectService'
import { UserStorageService } from '@/services/userStorage'
import { sessionManager } from '@/services/sessionManager'
import { CtpConfigManager } from '@/config/ctpConfig'

export interface DiagnosticResult {
  step: string
  status: 'success' | 'error' | 'warning'
  message: string
  details?: any
}

export class CtpDiagnostic {
  private results: DiagnosticResult[] = []

  /**
   * 执行完整的 CTP 连接诊断
   */
  async runFullDiagnostic(): Promise<DiagnosticResult[]> {
    this.results = []
    
    console.log('🔍 开始 CTP 连接诊断...')
    
    // 1. 检查用户登录信息
    await this.checkUserInfo()
    
    // 2. 检查会话状态
    await this.checkSessionStatus()
    
    // 3. 检查 CTP 服务状态
    await this.checkCtpServiceStatus()
    
    // 4. 检查交易 API 连接
    await this.checkTraderConnection()
    
    // 5. 尝试查询合约
    await this.testInstrumentQuery()
    
    // 6. 检查网络连接
    await this.checkNetworkConnectivity()
    
    console.log('✅ CTP 连接诊断完成')
    return this.results
  }

  /**
   * 检查用户登录信息
   */
  private async checkUserInfo(): Promise<void> {
    try {
      const userInfo = UserStorageService.getUserInfo()
      
      if (!userInfo) {
        this.addResult('用户信息检查', 'error', '未找到用户登录信息')
        return
      }
      
      if (!userInfo.account || !userInfo.password) {
        this.addResult('用户信息检查', 'error', '用户账号或密码为空')
        return
      }
      
      // 获取服务器配置
      const serverConfig = CtpConfigManager.getCurrentServerConfig()

      this.addResult('用户信息检查', 'success', `用户: ${userInfo.account}`, {
        account: userInfo.account,
        hasPassword: !!userInfo.password,
        server: userInfo.server,
        brokerId: serverConfig.brokerId,
        tradeFront: serverConfig.tradeFront,
        marketFront: serverConfig.marketFront
      })
    } catch (error) {
      this.addResult('用户信息检查', 'error', `检查用户信息失败: ${error}`)
    }
  }

  /**
   * 检查会话状态
   */
  private async checkSessionStatus(): Promise<void> {
    try {
      const mdSessionId = ctpService.getMdSessionId()
      const traderSessionId = ctpService.getTraderSessionId()
      
      this.addResult('会话状态检查', 'success', '会话状态', {
        mdSessionId: mdSessionId || '未创建',
        traderSessionId: traderSessionId || '未创建'
      })
      
      // 检查缓存的会话
      const cachedTraderSession = await sessionManager.getValidTraderSessionId()
      if (cachedTraderSession) {
        this.addResult('缓存会话检查', 'success', `找到缓存的交易会话: ${cachedTraderSession}`)
      } else {
        this.addResult('缓存会话检查', 'warning', '未找到有效的缓存会话')
      }
    } catch (error) {
      this.addResult('会话状态检查', 'error', `检查会话状态失败: ${error}`)
    }
  }

  /**
   * 检查 CTP 服务状态
   */
  private async checkCtpServiceStatus(): Promise<void> {
    try {
      const mdStatus = ctpService.getMdStatus()
      const traderStatus = ctpService.getTraderStatus()
      
      this.addResult('CTP 服务状态', 'success', 'CTP 服务状态', {
        mdStatus,
        traderStatus
      })
      
      if (traderStatus !== 'login_success') {
        this.addResult('交易状态检查', 'warning', `交易状态异常: ${traderStatus}`)
      }
    } catch (error) {
      this.addResult('CTP 服务状态', 'error', `检查 CTP 服务状态失败: ${error}`)
    }
  }

  /**
   * 检查交易 API 连接
   */
  private async checkTraderConnection(): Promise<void> {
    try {
      console.log('🔍 检查交易 API 连接...')
      
      const isConnected = await autoReconnectService.ensureTraderConnection()
      
      if (isConnected) {
        this.addResult('交易连接检查', 'success', '交易 API 连接正常')
      } else {
        this.addResult('交易连接检查', 'error', '交易 API 连接失败')
      }
    } catch (error) {
      this.addResult('交易连接检查', 'error', `检查交易连接失败: ${error}`)
    }
  }

  /**
   * 测试合约查询
   */
  private async testInstrumentQuery(): Promise<void> {
    try {
      console.log('🔍 测试合约查询...')
      
      const result = await ctpService.queryInstruments()
      
      if (result.success && result.data) {
        this.addResult('合约查询测试', 'success', `成功获取 ${result.data.length} 个合约`)
      } else {
        this.addResult('合约查询测试', 'error', `合约查询失败: ${result.error}`)
      }
    } catch (error) {
      this.addResult('合约查询测试', 'error', `合约查询异常: ${error}`)
    }
  }

  /**
   * 检查网络连接
   */
  private async checkNetworkConnectivity(): Promise<void> {
    try {
      const userInfo = UserStorageService.getUserInfo()
      if (!userInfo) {
        this.addResult('网络连接检查', 'warning', '无法检查网络连接：缺少用户信息')
        return
      }

      // 简单的网络连接检查
      const isOnline = navigator.onLine
      this.addResult('网络连接检查', isOnline ? 'success' : 'error', 
        isOnline ? '网络连接正常' : '网络连接异常')
      
      // 检查 CTP 服务器地址格式
      const serverConfig = CtpConfigManager.getCurrentServerConfig()

      if (serverConfig.tradeFront) {
        const isValidFormat = /^tcp:\/\/[\w\.-]+:\d+$/.test(serverConfig.tradeFront)
        this.addResult('服务器地址检查', isValidFormat ? 'success' : 'warning',
          `交易服务器: ${serverConfig.tradeFront}`)
      }

      if (serverConfig.marketFront) {
        const isValidFormat = /^tcp:\/\/[\w\.-]+:\d+$/.test(serverConfig.marketFront)
        this.addResult('服务器地址检查', isValidFormat ? 'success' : 'warning',
          `行情服务器: ${serverConfig.marketFront}`)
      }
    } catch (error) {
      this.addResult('网络连接检查', 'error', `网络连接检查失败: ${error}`)
    }
  }

  /**
   * 添加诊断结果
   */
  private addResult(step: string, status: 'success' | 'error' | 'warning', message: string, details?: any): void {
    const result: DiagnosticResult = { step, status, message, details }
    this.results.push(result)
    
    const emoji = status === 'success' ? '✅' : status === 'error' ? '❌' : '⚠️'
    console.log(`${emoji} [${step}] ${message}`, details || '')
  }

  /**
   * 获取诊断结果摘要
   */
  getSummary(): { total: number, success: number, error: number, warning: number } {
    const total = this.results.length
    const success = this.results.filter(r => r.status === 'success').length
    const error = this.results.filter(r => r.status === 'error').length
    const warning = this.results.filter(r => r.status === 'warning').length
    
    return { total, success, error, warning }
  }

  /**
   * 生成诊断报告
   */
  generateReport(): string {
    const summary = this.getSummary()
    let report = `CTP 连接诊断报告\n`
    report += `==================\n`
    report += `总检查项: ${summary.total}\n`
    report += `成功: ${summary.success}, 错误: ${summary.error}, 警告: ${summary.warning}\n\n`
    
    this.results.forEach(result => {
      const emoji = result.status === 'success' ? '✅' : result.status === 'error' ? '❌' : '⚠️'
      report += `${emoji} ${result.step}: ${result.message}\n`
      if (result.details) {
        report += `   详情: ${JSON.stringify(result.details, null, 2)}\n`
      }
      report += '\n'
    })
    
    return report
  }
}

// 导出单例实例
export const ctpDiagnostic = new CtpDiagnostic()
