<template>
  <div class="left-control-panel" @click="handlePanelClick">
    <!-- 合约代码显示 -->
    <div class="contract-code-display">
      {{ currentContract?.code || panelContract?.code || '未选择' }}
    </div>

    <div class="contract-code-controls">
      <div>{{ fontSize }}</div>
      <!-- 缩放控制 -->
      <div class="zoom-controls">
        <button @click="zoomOut" class="zoom-btn">-</button>
        <button @click="zoomIn" class="zoom-btn">+</button>
      </div>
    </div>

    <div class="current-time">{{ currentTime }}</div>

    <!-- 价格变化信息 -->
    <div class="price-info-section">
      <div class="price-change-display">
        <div class="price-change-value"
          :class="{ negative: priceChangePercent.startsWith('-'), positive: !priceChangePercent.startsWith('-') && priceChangePercent !== '' }">
          {{ priceChangePercent || '0.00' }}%</div>
      </div>

      <div class="market-stats">
        <div class="stat-item">{{ totalVolume || 0 }}</div>
        <div class="stat-item">{{ totalPosition || 0 }}</div>
        <div class="stat-item">{{ dailyPositionChange || 0 }}</div>
      </div>
    </div>

   
    <!-- 下单数量输入 -->
    <div class="order-inputs">
      <div class="input-group">
        <input :value="lightOrderQuantity" @input="handleLightQuantityChange"
          type="number" class="order-input" placeholder="1" min="1" max="5"
          title="轻仓下单数量（手）- 鼠标左键下单" />
      </div>
      <div class="input-group">
        <input :value="heavyOrderQuantity" @input="handleHeavyQuantityChange"
          type="number" class="order-input" placeholder="20" min="5" max="25"
          title="重仓下单数量（手）- 鼠标右键下单" />
      </div>
    </div>

    <!-- 订单类型选择 -->
    <div class="order-type-group">
      <label class="radio-label">
        <input type="radio" :checked="orderType === 'A'" @change="$emit('update:orderType', 'A')" value="A" />
        <span class="radio-text">Order(A)</span>
      </label>
      <label class="radio-label">
        <input type="radio" :checked="orderType === 'B'" @change="$emit('update:orderType', 'B')" value="B" />
        <span class="radio-text">Order(B)</span>
      </label>
    </div>

    <!-- 交易选项 -->
    <div class="order-options">
      <label class="checkbox-label">
        <input type="checkbox" :checked="options.autoHand" @change="handleOptionChange('autoHand', $event)" />
        <span class="checkbox-text">金手指！</span>
      </label>
      <label class="checkbox-label">
        <input type="checkbox" :checked="options.cLimit499" @change="handleOptionChange('cLimit499', $event)" />
        <span class="checkbox-text">CLimit 499</span>
      </label>
      <label class="checkbox-label">
        <input type="checkbox" :checked="options.noLimit" @change="handleOptionChange('noLimit', $event)" />
        <span class="checkbox-text">无限制</span>
      </label>
      <label class="checkbox-label">
        <input type="checkbox" :checked="options.noCombo" @change="handleOptionChange('noCombo', $event)" />
        <span class="checkbox-text">NoCombo</span>
      </label>
      <label class="checkbox-label">
        <input type="checkbox" :checked="options.upLimit" @change="handleOptionChange('upLimit', $event)" />
        <span class="checkbox-text">UpLimit</span>
      </label>
    </div>

    <!-- 交易操作按钮 -->
    <!-- <div class="trading-buttons">
      <button @click="handleBuyLong" class="trading-btn buy-long-btn" title="买入多单（做多开仓）">
        买入多单
      </button>
      <button @click="handleSellShort" class="trading-btn sell-short-btn" title="买入空单（卖出开仓）">
        买入空单
      </button>
      <button @click="handleCancel" class="trading-btn cancel-btn" :disabled="!hasActiveOrders" title="撤销最新订单">
        撤销
      </button>
      <button @click="handleCancelAll" class="trading-btn cancel-all-btn" :disabled="!hasActiveOrders" title="撤销所有挂单">
        全撤
      </button>
    </div> -->

    <!-- 原有撤单操作按钮（保留用于账号全撤） -->
    <!-- <div class="cancel-buttons">
      <button @click="$emit('cancel-all-account-orders')" class="cancel-account-btn" title="撤销账号下所有挂单">
        账号全撤
      </button>
      <button @click="$emit('test-position')" class="test-position-btn" title="测试持仓数据查询">
        测试持仓
      </button>
    </div> -->
     <!-- 个人持仓量显示 -->
    <div class="zero-values">
      <div class="zero-value red" :title="`空头持仓: ${Math.abs(redValue || 0)}手`">{{ redValue || 0 }}</div>
      <div class="zero-value blue" :title="`多头持仓: ${blueValue || 0}手`">{{ blueValue || 0 }}</div>
    </div>

    <!-- 持仓信息 -->
    <div class="position-info-section">
      <div class="position-line" title="净持仓 = 多头持仓 - 空头持仓">净仓: {{ netPosition || 0 }}</div>
      <div class="position-line" title="C: 平仓相关持仓, T: 今日持仓">C: {{ cPosition || 0 }} T: {{ tPosition || 0 }}</div>
    </div>

    <!-- 盈亏显示 -->
    <div class="pnl-display">
      <div class="pnl-value">{{ pnlValue || 0 }}</div>
      <div class="pnl-letter">P</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ContractInfo } from '@/types/trading'

// Props
const props = defineProps<{
  currentContract?: ContractInfo | null
  panelContract?: ContractInfo | null
  fontSize: number
  currentTime: string
  priceChangePercent: string
  totalVolume: number
  totalPosition: number
  dailyPositionChange: number
  redValue: number
  blueValue: number
  lightOrderQuantity: number
  heavyOrderQuantity: number
  orderType: string
  options: {
    autoHand: boolean
    cLimit499: boolean
    noLimit: boolean
    noCombo: boolean
    upLimit: boolean
  }
  netPosition: number
  cPosition: number
  tPosition: number
  pnlValue: number
  hasActiveOrders: boolean
}>()

// Emits
const emit = defineEmits<{
  zoomIn: []
  zoomOut: []
  'update:lightOrderQuantity': [value: number]
  'update:heavyOrderQuantity': [value: number]
  'update:orderType': [value: string]
  'update:options': [value: any]
  'cancel-all-orders': []
  'cancel-all-account-orders': []
  'buy-long': []
  'sell-short': []
  'cancel-order': []
  'cancel-all': []
  'test-position': []
  'panel-click': [event: MouseEvent]
}>()

// Methods
const zoomIn = () => {
  emit('zoomIn')
}

const zoomOut = () => {
  emit('zoomOut')
}

// 处理输入变化
const handleLightQuantityChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:lightOrderQuantity', Number(target.value))
}

const handleHeavyQuantityChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:heavyOrderQuantity', Number(target.value))
}

// 处理选项更新
const updateOption = (key: string, value: boolean) => {
  const newOptions = { ...props.options }
  newOptions[key] = value
  emit('update:options', newOptions)
}

const handleOptionChange = (key: string, event: Event) => {
  const target = event.target as HTMLInputElement
  updateOption(key, target.checked)
}

// 交易按钮处理函数
const handleBuyLong = () => {
  console.log('🔵 [LeftControlPanel] 点击买入多单按钮')
  console.log('🔵 [LeftControlPanel] 准备发送 buy-long 事件')
  try {
    emit('buy-long')
    console.log('✅ [LeftControlPanel] buy-long 事件发送成功')
  } catch (error) {
    console.error('❌ [LeftControlPanel] buy-long 事件发送失败:', error)
  }
}

const handleSellShort = () => {
  console.log('🔴 [LeftControlPanel] 点击买入空单按钮')
  console.log('🔴 [LeftControlPanel] 准备发送 sell-short 事件')
  try {
    emit('sell-short')
    console.log('✅ [LeftControlPanel] sell-short 事件发送成功')
  } catch (error) {
    console.error('❌ [LeftControlPanel] sell-short 事件发送失败:', error)
  }
}

const handleCancel = () => {
  console.log('🟡 点击撤销按钮')
  emit('cancel-order')
}

const handleCancelAll = () => {
  console.log('🟠 点击全撤按钮')
  emit('cancel-all')
}

// 处理面板点击事件
const handlePanelClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement

  // 检查点击的是否为输入框、按钮或其他交互元素
  const isInteractiveElement = target.tagName === 'INPUT' ||
                               target.tagName === 'BUTTON' ||
                               target.closest('button') ||
                               target.closest('input') ||
                               target.closest('.zoom-btn') ||
                               target.closest('.trading-btn') ||
                               target.closest('.cancel-account-btn') ||
                               target.closest('.test-position-btn') ||
                               target.closest('.checkbox-label')

  // 如果点击的不是交互元素，则发送面板点击事件
  if (!isInteractiveElement) {
    console.log('🖱️ 点击LeftControlPanel非交互区域，取消表格滚动')
    emit('panel-click', event)
  }
}
</script>

<style scoped>
/* 左侧操作列 */
.left-control-panel {
  width: 90px;
  min-width: 90px;
  /* 移除最大宽度限制，允许在窗口缩放时适应 */
  background: #c0c0c0;
  border: 1px inset #c0c0c0;
  padding: 4px;
  display: flex;
  flex-direction: column;
  gap: 3px;
  flex-shrink: 0;
  box-sizing: border-box;
}

.contract-code-controls {
  display: flex;
  justify-content: space-between;
}

/* 合约代码显示 */
.contract-code-display {
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 2px 4px;
  text-align: center;
  font-weight: bold;
  font-size: 10px;
  color: #000000;
}

/* 缩放控制 */
.zoom-controls {
  display: flex;
  gap: 1px;
  justify-content: center;
}

/* 当前时间显示 */
.current-time {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: bold;
  color: #333;
  background: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 3px;
  padding: 3px 0;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
  min-width: 80px;
}

.zoom-btn {
  width: 16px;
  height: 16px;
  border: 1px outset #c0c0c0;
  background: #c0c0c0;
  cursor: pointer;
  font-size: 9px;
  font-weight: bold;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-btn:active {
  border: 1px inset #c0c0c0;
}

/* 价格信息区域 */
.price-info-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.price-change-display {
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 2px;
  text-align: center;
}

.price-change-value {
  font-size: 10px;
  font-weight: bold;
}

.price-change-value.negative {
  color: #ff0000;
}

.price-change-value.positive {
  color: #008000;
  font-size: 20px;
}

/* 市场统计数据 */
.market-stats {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.stat-item {
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 1px 2px;
  text-align: center;
  font-size: 20px;
  color: #000000;
}

/* 红蓝数值显示 */
.zero-values {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.zero-value {
  height: 18px;
  line-height: 18px;
  text-align: center;
  color: white;
  font-size: 10px;
  font-weight: bold;
  border: 1px inset #c0c0c0;
}

.zero-value.red {
  background: #ff0000;
}

.zero-value.blue {
  background: #0000ff;
}

/* 下单输入框 */
.order-inputs {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.input-group {
  display: flex;
  justify-content: center;
}

.order-input {
  width: 60px;
  height: 18px;
  padding: 1px 3px;
  border: 1px inset #c0c0c0;
  background: #ffffff;
  text-align: center;
  font-size: 10px;
  color: #000000;
  font-family: 'MS Sans Serif', sans-serif;
}

.order-input:focus {
  outline: none;
  border: 1px inset #0000ff;
}

/* 去掉input number类型的上下箭头 */
.order-input::-webkit-outer-spin-button,
.order-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox浏览器去掉上下箭头 */
.order-input[type=number] {
  -moz-appearance: textfield;
  appearance: textfield;
}

/* 订单类型选择 */
.order-type-group {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.radio-label {
  display: flex;
  align-items: center;
  font-size: 9px;
  color: #000000;
  cursor: pointer;
}

.radio-label input[type="radio"] {
  margin-right: 4px;
  width: 12px;
  height: 12px;
}

.radio-text {
  user-select: none;
}

/* 交易选项 */
.order-options {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 9px;
  color: #000000;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 4px;
  width: 12px;
  height: 12px;
}

.checkbox-text {
  user-select: none;
}

/* 持仓信息 */
.position-info-section {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.position-line {
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 1px 3px;
  font-size: 9px;
  color: #000000;
  text-align: left;
}

/* 交易按钮 */
.trading-buttons {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin: 3px 0;
}

.trading-btn {
  height: 22px;
  border: 1px outset #c0c0c0;
  cursor: pointer;
  font-size: 9px;
  font-weight: bold;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  transition: all 0.1s ease;
}

.buy-long-btn {
  background: #ff4444; /* 红色 - 买入多单 */
}

.buy-long-btn:hover {
  background: #dd3333;
}

.buy-long-btn:active {
  border: 1px inset #c0c0c0;
  background: #cc2222;
}

.sell-short-btn {
  background: #44aa44; /* 绿色 - 买入空单 */
}

.sell-short-btn:hover {
  background: #339933;
}

.sell-short-btn:active {
  border: 1px inset #c0c0c0;
  background: #228822;
}

.trading-btn.cancel-btn {
  background: #ffaa00; /* 橙色 - 撤销 */
}

.trading-btn.cancel-btn:hover:not(:disabled) {
  background: #dd9900;
}

.trading-btn.cancel-btn:active:not(:disabled) {
  border: 1px inset #c0c0c0;
  background: #cc8800;
}

.trading-btn.cancel-all-btn {
  background: #aa4444; /* 深红色 - 全撤 */
}

.trading-btn.cancel-all-btn:hover:not(:disabled) {
  background: #993333;
}

.trading-btn.cancel-all-btn:active:not(:disabled) {
  border: 1px inset #c0c0c0;
  background: #882222;
}

.trading-btn:disabled {
  background: #e0e0e0;
  color: #999999;
  cursor: not-allowed;
  opacity: 0.6;
}

/* 撤单按钮 */
.cancel-buttons {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin: 3px 0;
}

.cancel-btn, .cancel-account-btn, .test-position-btn {
  height: 20px;
  border: 1px outset #c0c0c0;
  background: #c0c0c0;
  cursor: pointer;
  font-size: 9px;
  font-weight: bold;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

.test-position-btn {
  background: #4CAF50; /* 绿色背景表示测试功能 */
  color: #ffffff;
}

.cancel-btn:hover:not(:disabled), .cancel-account-btn:hover:not(:disabled) {
  background: #a0a0a0;
}

.test-position-btn:hover:not(:disabled) {
  background: #45a049;
}

.cancel-btn:active:not(:disabled), .cancel-account-btn:active:not(:disabled) {
  border: 1px inset #c0c0c0;
}

.test-position-btn:active:not(:disabled) {
  border: 1px inset #c0c0c0;
  background: #3d8b40;
}

.cancel-btn:disabled, .cancel-account-btn:disabled, .test-position-btn:disabled {
  background: #e0e0e0;
  color: #999999;
  cursor: not-allowed;
  opacity: 0.6;
}

/* 盈亏显示 */
.pnl-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 3px;
  margin-top: auto;
}

.pnl-value {
  font-size: 12px;
  font-weight: bold;
  color: #000000;
}

.pnl-letter {
  font-size: 14px;
  font-weight: bold;
  color: #000000;
}

/* 滚动条样式 */
.left-control-panel::-webkit-scrollbar {
  width: 5px;
}

.left-control-panel::-webkit-scrollbar-track {
  background: #c0c0c0;
  border-radius: 4px;
}

.left-control-panel::-webkit-scrollbar-thumb {
  background: #808080;
  border-radius: 4px;
}

.left-control-panel::-webkit-scrollbar-thumb:hover {
  background: #606060;
}
</style>
