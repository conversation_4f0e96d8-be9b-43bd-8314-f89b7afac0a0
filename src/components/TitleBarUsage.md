# 自定义标题栏组件使用指南

## 概述

`TitleBar` 是一个功能完整的自定义标题栏组件，专为 Tauri 应用程序设计。它提供了窗口控制、主题定制、自定义按钮等功能。

## 基础用法

### 1. 最简单的使用

```vue
<template>
  <TitleBar title="我的应用" />
</template>

<script setup>
import TitleBar from '@/components/TitleBar.vue'
</script>
```

### 2. 自定义样式

```vue
<template>
  <TitleBar 
    title="自定义样式"
    :height="'40px'"
    :background-color="'#1f1f1f'"
    :text-color="'#ffffff'"
  />
</template>
```

### 3. 使用预定义主题

```vue
<template>
  <TitleBar 
    title="主题示例"
    theme="primary"
  />
</template>
```

可用主题：`dark`、`light`、`primary`、`success`、`warning`、`danger`

## 高级功能

### 1. 自定义按钮

```vue
<template>
  <TitleBar 
    title="带自定义按钮"
    :custom-buttons="customButtons"
    @custom-button-click="handleCustomButtonClick"
  />
</template>

<script setup>
import { ref } from 'vue'
import TitleBar from '@/components/TitleBar.vue'

const customButtons = ref([
  {
    id: 'settings',
    icon: '<svg>...</svg>',
    tooltip: '设置',
    onClick: () => console.log('设置被点击')
  },
  {
    id: 'help',
    text: '帮助',
    tooltip: '获取帮助',
    onClick: () => console.log('帮助被点击')
  }
])

const handleCustomButtonClick = (button) => {
  console.log('自定义按钮被点击:', button.id)
}
</script>
```

### 2. 使用插槽

```vue
<template>
  <TitleBar title="插槽示例">
    <!-- 中间内容插槽 -->
    <template #center>
      <div class="status">
        <span class="dot"></span>
        <span>已连接</span>
      </div>
    </template>
    
    <!-- 自定义按钮插槽 -->
    <template #buttons>
      <button @click="showMenu">菜单</button>
      <button @click="showNotifications">通知</button>
    </template>
  </TitleBar>
</template>
```

### 3. 事件处理

```vue
<template>
  <TitleBar 
    title="事件处理"
    @minimize="onMinimize"
    @maximize="onMaximize"
    @restore="onRestore"
    @close="onClose"
    @double-click="onDoubleClick"
    @context-menu="onContextMenu"
  />
</template>

<script setup>
const onMinimize = () => console.log('窗口最小化')
const onMaximize = () => console.log('窗口最大化')
const onRestore = () => console.log('窗口还原')
const onClose = () => console.log('窗口关闭')
const onDoubleClick = () => console.log('双击标题栏')
const onContextMenu = (event) => console.log('右键菜单', event)
</script>
```

## 配置选项

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `title` | `string` | `'Tauri App'` | 窗口标题 |
| `showIcon` | `boolean` | `true` | 是否显示图标 |
| `iconSrc` | `string` | `'/tauri.svg'` | 图标路径 |
| `showControls` | `boolean` | `true` | 是否显示窗口控制按钮 |
| `height` | `string` | `'32px'` | 标题栏高度 |
| `backgroundColor` | `string` | `'#2c2c2c'` | 背景颜色 |
| `textColor` | `string` | `'#ffffff'` | 文字颜色 |
| `theme` | `string \| TitleBarTheme` | `'dark'` | 主题配置 |
| `customButtons` | `CustomButton[]` | `[]` | 自定义按钮数组 |
| `enableDoubleClickMaximize` | `boolean` | `true` | 是否启用双击最大化 |
| `enableRightClickMenu` | `boolean` | `false` | 是否启用右键菜单 |

### Events

| 事件 | 参数 | 说明 |
|------|------|------|
| `minimize` | - | 窗口最小化时触发 |
| `maximize` | - | 窗口最大化时触发 |
| `restore` | - | 窗口还原时触发 |
| `close` | - | 窗口关闭时触发 |
| `doubleClick` | - | 双击标题栏时触发 |
| `contextMenu` | `MouseEvent` | 右键菜单时触发 |
| `customButtonClick` | `CustomButton` | 自定义按钮点击时触发 |

### Slots

| 插槽 | 说明 |
|------|------|
| `center` | 中间区域内容 |
| `buttons` | 自定义按钮区域 |

## 类型定义

### CustomButton

```typescript
interface CustomButton {
  id: string
  icon?: string
  text?: string
  tooltip?: string
  onClick: () => void
  disabled?: boolean
  visible?: boolean
  style?: Record<string, string>
  className?: string
}
```

### TitleBarTheme

```typescript
interface TitleBarTheme {
  backgroundColor: string
  textColor: string
  height: string
  borderColor?: string
  hoverColor?: string
  activeColor?: string
}
```

## 实际应用示例

### 1. 交易面板标题栏

```vue
<template>
  <TitleBar 
    title="交易面板"
    theme="dark"
    :custom-buttons="tradingButtons"
    @custom-button-click="handleTradingAction"
  >
    <template #center>
      <div class="connection-status">
        <span :class="['status-dot', connectionStatus]"></span>
        <span>{{ connectionText }}</span>
      </div>
    </template>
  </TitleBar>
</template>

<script setup>
import { ref, computed } from 'vue'

const isConnected = ref(true)
const connectionStatus = computed(() => isConnected.value ? 'connected' : 'disconnected')
const connectionText = computed(() => isConnected.value ? '已连接' : '未连接')

const tradingButtons = ref([
  {
    id: 'connect',
    icon: '🔗',
    tooltip: '连接/断开',
    onClick: () => isConnected.value = !isConnected.value
  },
  {
    id: 'settings',
    icon: '⚙️',
    tooltip: '设置',
    onClick: () => console.log('打开设置')
  }
])
</script>
```

### 2. 数据面板标题栏

```vue
<template>
  <TitleBar 
    title="实时数据"
    theme="primary"
    :enable-double-click-maximize="false"
  >
    <template #center>
      <div class="data-info">
        <span>更新时间: {{ lastUpdate }}</span>
      </div>
    </template>
    
    <template #buttons>
      <button @click="refreshData" title="刷新数据">🔄</button>
      <button @click="exportData" title="导出数据">📊</button>
    </template>
  </TitleBar>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const lastUpdate = ref('')

const updateTime = () => {
  lastUpdate.value = new Date().toLocaleTimeString()
}

const refreshData = () => {
  updateTime()
  console.log('刷新数据')
}

const exportData = () => {
  console.log('导出数据')
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
})
</script>
```

## 注意事项

1. **Tauri 配置**: 确保在 `tauri.conf.json` 中设置 `"decorations": false`
2. **拖拽区域**: 组件自动设置 `data-tauri-drag-region` 属性
3. **事件冒泡**: 按钮点击事件会阻止拖拽，确保正常的交互体验
4. **主题一致性**: 建议在整个应用中保持主题一致性
5. **响应式**: 组件支持响应式设计，在小屏幕上会自动调整

## 最佳实践

1. **性能优化**: 避免在标题栏中放置过多复杂组件
2. **用户体验**: 保持标题栏简洁，重要功能优先
3. **主题管理**: 使用统一的主题管理系统
4. **事件处理**: 合理处理窗口事件，避免意外关闭
5. **可访问性**: 为按钮提供合适的 tooltip 和 aria 标签
