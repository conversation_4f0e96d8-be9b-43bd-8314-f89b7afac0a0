import { ref } from 'vue'
import { ctpService } from '../services/ctpService'
import { contractService } from '../services/contractService'
import TimeService from '../services/timeService'

export function useMarketData() {
  // 为每个面板实例创建独立的标识符
  const panelInstanceId = `panel_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  console.log(`🆔 创建新的行情数据实例: ${panelInstanceId}`)

  // 市场数据状态
  const isUsingRealData = ref(false)
  const currentPrice = ref(0)
  const priceDirection = ref('neutral')
  const priceChangePercent = ref('')
  const totalVolume = ref(0)
  const totalPosition = ref(0)
  const dailyPositionChange = ref(0)
  const redValue = ref(0)
  const blueValue = ref(0)

  // 涨跌停价格
  const upperLimitPrice = ref(0)  // 涨停价
  const lowerLimitPrice = ref(0)  // 跌停价

  // 特殊价格
  const openPrice = ref(0)        // 开盘价
  const highestPrice = ref(0)     // 最高价
  const lowestPrice = ref(0)      // 最低价
  const bid1Price = ref(0)        // 买一价
  const ask1Price = ref(0)        // 卖一价

  // 价格档位数据
  const sellOrders = ref([])
  const buyOrders = ref([])
  const marketDataMap = ref(new Map())
  const processedOrderBook = ref({
    sell: [],
    buy: []
  })

  // 价格档位配置
  const PRICE_LEVELS = ref({
    SELL_LEVELS: 0,
    BUY_LEVELS: 0,
    PRICE_STEP: 1
  })

  // 每个面板实例的独立订阅管理
  const currentSubscribedContract = ref('')  // 当前面板订阅的合约
  let marketDataListener = null  // 当前面板的独立监听器
  let isUpdatingOrders = false

  // 行情数据时间戳
  const lastUpdateTime = ref('')
  const lastUpdateTimestamp = ref(0)

  // 动态计算表格行数
  const calculateTableRows = (tableContainer) => {
    if (!tableContainer) return

    const containerHeight = tableContainer.clientHeight
    const separatorHeight = 0
    const rowHeight = 18
    const availableHeight = containerHeight - separatorHeight
    const totalRows = Math.ceil(availableHeight / rowHeight)
    const sellLevels = Math.floor(totalRows / 2)
    const buyLevels = Math.floor(totalRows / 2)
    const remainingRows = totalRows - sellLevels - buyLevels
    const finalSellLevels = sellLevels + remainingRows
    const finalBuyLevels = buyLevels

    PRICE_LEVELS.value.SELL_LEVELS = Math.max(finalSellLevels, 1)
    PRICE_LEVELS.value.BUY_LEVELS = Math.max(finalBuyLevels, 1)

    console.log('📏 动态计算表格行数:', {
      容器高度: containerHeight,
      可用高度: availableHeight,
      总行数: totalRows,
      最终卖盘行数: PRICE_LEVELS.value.SELL_LEVELS,
      最终买盘行数: PRICE_LEVELS.value.BUY_LEVELS
    })
  }

  // 价格连续性检查
  const validatePriceContinuity = (prices, context = '') => {
    if (prices.length < 2) return true

    let isValid = true
    const issues = []

    for (let i = 1; i < prices.length; i++) {
      const priceDiff = Math.abs(prices[i - 1] - prices[i])
      const expectedDiff = PRICE_LEVELS.value.PRICE_STEP

      if (Math.abs(priceDiff - expectedDiff) > 0.001) {
        isValid = false
        issues.push(`位置${i}: ${prices[i - 1]} -> ${prices[i]} (差值: ${priceDiff}, 期望: ${expectedDiff})`)
      }
    }

    if (!isValid) {
      console.error(`❌ ${context} 价格连续性检查失败:`, {
        总价格数: prices.length,
        价格范围: `${prices[0]} - ${prices[prices.length - 1]}`,
        问题档位: issues,
        价格步长: PRICE_LEVELS.value.PRICE_STEP
      })
    } else {
      console.log(`✅ ${context} 价格连续性检查通过:`, {
        总价格数: prices.length,
        价格范围: `${prices[0]} - ${prices[prices.length - 1]}`,
        价格步长: PRICE_LEVELS.value.PRICE_STEP
      })
    }

    return isValid
  }

  // 填补价格缺口
  const fillPriceGaps = (prices) => {
    if (prices.length < 2) return prices

    const sortedPrices = [...prices].sort((a, b) => b - a)
    const filledPrices = []

    for (let i = 0; i < sortedPrices.length - 1; i++) {
      filledPrices.push(sortedPrices[i])

      const currentPrice = sortedPrices[i]
      const nextPrice = sortedPrices[i + 1]
      const gap = currentPrice - nextPrice
      const expectedGap = PRICE_LEVELS.value.PRICE_STEP

      if (gap > expectedGap + 0.001) {
        const missingSteps = Math.round(gap / expectedGap) - 1
        for (let step = 1; step <= missingSteps; step++) {
          const missingPrice = currentPrice - step * expectedGap
          filledPrices.push(missingPrice)
          console.log(`🔧 填补价格缺口: ${missingPrice} (在 ${currentPrice} 和 ${nextPrice} 之间)`)
        }
      }
    }

    filledPrices.push(sortedPrices[sortedPrices.length - 1])
    return filledPrices.sort((a, b) => b - a)
  }

  // 颜色枚举
  const PriceColor = {
    RED: '#a33a66ff',
    BLUE: '#409b7eff',
    GRAY: 'gray',
    YELLOW: 'blue',      // 最高价  // 空单成交后 #aba84bff
    GREEN: 'red',        // 最低价   // 多单成交后：#d07777ff
    LIGHT_GRAY: '#2f3231ff' // 开盘价    // 最右侧的颜色 ：
  }

  // 获取价格背景颜色（包括特殊价格）
  const getPriceBackgroundColor = (price, bidPrice1, askPrice1) => {
    const roundedPrice = Math.round(price)
    const roundedBidPrice1 = Math.round(bidPrice1 || 0)
    const roundedAskPrice1 = Math.round(askPrice1 || 0)

    // 检查特殊价格
    if (highestPrice.value > 0 && roundedPrice === highestPrice.value) {
      return PriceColor.YELLOW  // 最高价：黄色
    }
    if (lowestPrice.value > 0 && roundedPrice === lowestPrice.value) {
      return PriceColor.GREEN   // 最低价：绿色
    }
    if (openPrice.value > 0 && roundedPrice === openPrice.value) {
      return PriceColor.LIGHT_GRAY  // 开盘价：浅灰色
    }

    // 常规价格颜色规则
    if (roundedAskPrice1 > 0 && roundedPrice >= roundedAskPrice1) {
      return PriceColor.RED     // 卖一价及以上：红色
    }
    if (roundedBidPrice1 > 0 && roundedPrice <= roundedBidPrice1) {
      return PriceColor.BLUE    // 买一价及以下：蓝色
    }

    return PriceColor.GRAY      // 买一价和卖一价之间：灰色
  }

  // 获取价格颜色
  const getPriceColor = (lastPrice, bidPrice, askPrice, prevLastPrice = null) => {
    if (lastPrice >= askPrice) return PriceColor.BLUE
    if (lastPrice <= bidPrice) return PriceColor.RED

    if (bidPrice < lastPrice && lastPrice < askPrice) {
      if (prevLastPrice !== null) {
        if (lastPrice > prevLastPrice) return PriceColor.BLUE
        if (lastPrice < prevLastPrice) return PriceColor.RED
      }
      return PriceColor.GRAY
    }

    return PriceColor.GRAY
  }

  // 渲染订单簿
  const renderOrderBook = (bids, asks, lastPrice, prevLastPrice = null) => {
    const result = []
    const allPrices = new Set()

    bids.forEach(([price, volume]) => {
      if (price > 0) allPrices.add(price)
    })

    asks.forEach(([price, volume]) => {
      if (price > 0) allPrices.add(price)
    })

    if (lastPrice > 0) allPrices.add(lastPrice)

    const sortedPrices = Array.from(allPrices).sort((a, b) => b - a)

    console.log('📊 价格连续性检查:', {
      总价格数: sortedPrices.length,
      最高价: sortedPrices[0],
      最低价: sortedPrices[sortedPrices.length - 1],
      最新价: lastPrice,
      价格范围: `${sortedPrices[sortedPrices.length - 1]} - ${sortedPrices[0]}`
    })

    const bidMap = new Map()
    const askMap = new Map()

    bids.forEach(([price, volume]) => {
      bidMap.set(price, volume)
    })

    asks.forEach(([price, volume]) => {
      askMap.set(price, volume)
    })

    sortedPrices.forEach(price => {
      const bidVolume = bidMap.get(price) || 0
      const askVolume = askMap.get(price) || 0

      let color = PriceColor.GRAY

      if (price === lastPrice) {
        color = PriceColor.GRAY
      } else if (price > lastPrice) {
        color = PriceColor.RED
        if (askVolume > 0 && price <= lastPrice) {
          color = PriceColor.BLUE
        }
      } else {
        color = PriceColor.BLUE
        if (bidVolume > 0 && price >= lastPrice) {
          color = PriceColor.RED
        }
      }

      if (prevLastPrice !== null && price === lastPrice) {
        color = lastPrice > prevLastPrice ? PriceColor.BLUE :
          lastPrice < prevLastPrice ? PriceColor.RED : PriceColor.GRAY
      }

      result.push({
        price,
        buyVolume: bidVolume,
        sellVolume: askVolume,
        color,
        level: '1'
      })
    })

    for (let i = 1; i < result.length; i++) {
      const priceDiff = result[i - 1].price - result[i].price
      if (priceDiff !== PRICE_LEVELS.value.PRICE_STEP && Math.abs(priceDiff) > 0.001) {
        console.warn('⚠️ renderOrderBook检测到价格不连续:', {
          前一价格: result[i - 1].price,
          当前价格: result[i].price,
          价格差: priceDiff,
          期望差值: PRICE_LEVELS.value.PRICE_STEP,
          索引: i
        })
      }
    }

    console.log('✅ renderOrderBook完成，价格连续性已验证')
    return result
  }

  // 生成空的价格档位
  const generateEmptyPriceOrders = () => {
    const emptySellOrders = []
    for (let i = 1; i <= PRICE_LEVELS.value.SELL_LEVELS; i++) {
      emptySellOrders.push({
        price: 0,
        buyVolume: 0,
        sellVolume: 0,
        level: i.toString()
      })
    }
    emptySellOrders.reverse()

    const emptyBuyOrders = []
    for (let i = 1; i <= PRICE_LEVELS.value.BUY_LEVELS; i++) {
      emptyBuyOrders.push({
        price: 0,
        buyVolume: 0,
        sellVolume: 0,
        level: i.toString()
      })
    }

    sellOrders.value = emptySellOrders
    buyOrders.value = emptyBuyOrders
  }

  // 更新当前价格
  const updateCurrentPrice = (newPrice) => {
    const oldPrice = currentPrice.value
    const newPriceRounded = Math.round(newPrice)

    if (newPriceRounded > oldPrice) {
      priceDirection.value = 'up'
    } else if (newPriceRounded < oldPrice) {
      priceDirection.value = 'down'
    } else {
      priceDirection.value = 'neutral'
    }

    currentPrice.value = newPriceRounded

    setTimeout(() => {
      priceDirection.value = 'neutral'
    }, 3000)
  }

  // 清理当前面板的订阅
  const cleanupCurrentSubscription = async () => {
    if (currentSubscribedContract.value) {
      console.log(`🧹 [${panelInstanceId}] 清理现有订阅:`, currentSubscribedContract.value)

      try {
        // 取消订阅当前合约（传递面板ID，支持多面板共享）
        const unsubscribeSuccess = await contractService.unsubscribeContractMarketData(currentSubscribedContract.value, panelInstanceId)
        if (unsubscribeSuccess) {
          console.log(`✅ [${panelInstanceId}] 成功取消订阅: ${currentSubscribedContract.value}`)
        } else {
          console.warn(`⚠️ [${panelInstanceId}] 取消订阅失败: ${currentSubscribedContract.value}`)
        }
      } catch (error) {
        console.error(`❌ [${panelInstanceId}] 取消订阅异常:`, error)
      }

      // 移除当前面板的事件监听器
      if (marketDataListener) {
        ctpService.off('market_data', marketDataListener)
        marketDataListener = null
        console.log(`🔇 [${panelInstanceId}] 已移除行情数据监听器`)
      }

      // 清空当前面板的订阅
      currentSubscribedContract.value = ''
    }
  }

  // 更新行情数据（只处理当前面板订阅的合约）
  const updateMarketData = (data) => {
    // 验证数据是否属于当前面板订阅的合约
    if (!data.instrument_id || data.instrument_id !== currentSubscribedContract.value) {
      console.log(`🚫 [${panelInstanceId}] 忽略非本面板合约的行情数据: ${data.instrument_id}, 当前订阅: ${currentSubscribedContract.value}`)
      return
    }

    console.log(`📈 [${panelInstanceId}] 更新行情数据:`, data)

    // 处理CTP时间戳
    if (data.update_time && data.update_millisec !== undefined) {
      try {
        const ctpTime = TimeService.formatCTPTime(data.update_time, data.update_millisec, {
          showMilliseconds: true,
          use24Hour: true,
          showDate: false
        })
        lastUpdateTime.value = ctpTime
        lastUpdateTimestamp.value = TimeService.parseCTPTime(data.update_time, data.update_millisec).getTime()
        console.log(`⏰ [${panelInstanceId}] CTP行情时间: ${ctpTime}`)
      } catch (error) {
        console.warn(`⚠️ [${panelInstanceId}] CTP时间解析失败:`, error)
      }
    }

    // 更新基础行情信息
    if (data.last_price && data.last_price > 0) {
      updateCurrentPrice(data.last_price)
    }

    // 更新价格变化百分比
    if (data.pre_settlement_price && data.pre_settlement_price > 0 && data.last_price && data.last_price > 0) {
      const changePercent = ((data.last_price - data.pre_settlement_price) / data.pre_settlement_price * 100).toFixed(2)
      priceChangePercent.value = changePercent
    }

    // 更新涨跌停价格
    if (data.upper_limit_price && data.upper_limit_price > 0) {
      upperLimitPrice.value = data.upper_limit_price
    }
    if (data.lower_limit_price && data.lower_limit_price > 0) {
      lowerLimitPrice.value = data.lower_limit_price
    }

    // 更新特殊价格
    if (data.open_price && data.open_price > 0) {
      openPrice.value = data.open_price
    }
    if (data.highest_price && data.highest_price > 0) {
      highestPrice.value = data.highest_price
    }
    if (data.lowest_price && data.lowest_price > 0) {
      lowestPrice.value = data.lowest_price
    }

    // 更新成交量和持仓量
    if (data.volume !== undefined) totalVolume.value = data.volume
    if (data.open_interest !== undefined) totalPosition.value = data.open_interest

    // 更新买一价和卖一价
    if (data.bid_price1 && data.bid_price1 > 0) {
      bid1Price.value = Math.round(data.bid_price1)
    }
    if (data.ask_price1 && data.ask_price1 > 0) {
      ask1Price.value = Math.round(data.ask_price1)
    }

    // 更新买卖盘数据到marketDataMap（处理5档行情数据）
    const price = Math.round(data.last_price || 0)
    if (price > 0) {
      // 更新5档买盘数据
      for (let i = 1; i <= 5; i++) {
        const bidPriceKey = `bid_price${i}`
        const bidVolumeKey = `bid_volume${i}`

        if (data[bidPriceKey] && data[bidPriceKey] > 0) {
          const bidPrice = Math.round(data[bidPriceKey])
          const existingData = marketDataMap.value.get(bidPrice) || { bidVolume: 0, askVolume: 0 }
          marketDataMap.value.set(bidPrice, {
            bidVolume: data[bidVolumeKey] || 0,
            askVolume: existingData.askVolume
          })
        }
      }

      // 更新5档卖盘数据
      for (let i = 1; i <= 5; i++) {
        const askPriceKey = `ask_price${i}`
        const askVolumeKey = `ask_volume${i}`

        if (data[askPriceKey] && data[askPriceKey] > 0) {
          const askPrice = Math.round(data[askPriceKey])
          const existingData = marketDataMap.value.get(askPrice) || { bidVolume: 0, askVolume: 0 }
          marketDataMap.value.set(askPrice, {
            bidVolume: existingData.bidVolume,
            askVolume: data[askVolumeKey] || 0
          })
        }
      }

      // 生成价格档位数据
      generatePriceOrders(data, data.bid_price1, data.ask_price1)
    }

    console.log('✅ 行情数据更新完成')
  }

  // 查询行情数据
  const queryMarketData = async (contractCode) => {
    try {
      console.log(`🔍 [${panelInstanceId}] 查询行情数据: ${contractCode}`)

      // 先清理现有订阅
      await cleanupCurrentSubscription()

      // 使用新的单个合约订阅方法，传递面板ID
      const subscribeSuccess = await contractService.subscribeContractMarketData(contractCode, panelInstanceId)

      if (subscribeSuccess) {
        console.log(`✅ [${panelInstanceId}] 行情数据订阅成功`)
        console.log(`已订阅 ${contractCode} 行情数据`)

        // 设置当前面板订阅的合约
        currentSubscribedContract.value = contractCode

        // 创建当前面板的独立行情数据监听器
        marketDataListener = (data) => {
          // 只处理当前面板订阅的合约数据
          if (data.instrument_id === contractCode) {
            console.log(`📈 [${panelInstanceId}] 收到行情数据:`, data)
            updateMarketData(data)
          }
        }

        // 监听行情数据更新
        console.log(`🔍 [${panelInstanceId}] 开始监听 market_data 事件`)
        ctpService.on('market_data', marketDataListener)
        console.log(`🔍 [${panelInstanceId}] market_data 事件监听器已注册`)

        // 如果5秒内没有收到行情数据，生成默认表格
        setTimeout(() => {
          if (processedOrderBook.value.sell.length === 0 && processedOrderBook.value.buy.length === 0) {
            console.log(`⚠️ [${panelInstanceId}] 5秒内未收到行情数据，生成默认表格`)
            generateDefaultPriceTable(contractCode)
          }
        }, 5000)

        return true
      } else {
        console.warn(`⚠️ [${panelInstanceId}] 订阅行情失败: ${contractCode}`)
        console.warn(`订阅行情失败`)
        // 订阅失败时也生成默认表格
        generateDefaultPriceTable(contractCode)
        return false
      }
    } catch (error) {
      console.error(`❌ [${panelInstanceId}] 查询行情数据失败: ${contractCode}`, error)
      console.error(`查询行情数据失败: ${error instanceof Error ? error.message : '未知错误'}`)
      // 出错时也生成默认表格
      generateDefaultPriceTable(contractCode)
      return false
    }
  }

  // 生成默认价格表格（当没有行情数据时）
  const generateDefaultPriceTable = (contractCode) => {
    console.log(`🔧 [${panelInstanceId}] 生成默认价格表格: ${contractCode}`)

    // 根据合约类型设置默认价格
    let basePrice = 3500 // 默认价格
    if (contractCode.startsWith('rb')) {
      basePrice = 3500 // 螺纹钢
    } else if (contractCode.startsWith('hc')) {
      basePrice = 3200 // 热卷
    } else if (contractCode.startsWith('a')) {
      basePrice = 4000 // 豆粕
    }

    const priceStep = PRICE_LEVELS.value.PRICE_STEP
    const sellLevels = 20
    const buyLevels = 20

    // 生成卖盘数据（价格从高到低）
    const sellData = []
    for (let i = 0; i < sellLevels; i++) {
      const price = basePrice + (sellLevels - i) * priceStep
      sellData.push({
        price: Math.round(price),
        buyVolume: 0,
        sellVolume: 0,
        level: `卖${i + 1}`,
        color: 'red'
      })
    }

    // 生成买盘数据（价格从高到低）
    const buyData = []
    for (let i = 0; i < buyLevels; i++) {
      const price = basePrice - i * priceStep
      buyData.push({
        price: Math.round(price),
        buyVolume: 0,
        sellVolume: 0,
        level: `买${i + 1}`,
        color: 'blue'
      })
    }

    // 更新处理后的订单簿数据
    processedOrderBook.value.sell = sellData
    processedOrderBook.value.buy = buyData

    console.log(`✅ [${panelInstanceId}] 默认价格表格生成完成:`, {
      卖盘数据: sellData.length,
      买盘数据: buyData.length,
      基准价格: basePrice,
      价格步长: priceStep
    })
  }

  // 生成价格档位数据（根据买一价和卖一价生成，补全间隔）
  const generatePriceOrders = (data, bidPrice1, askPrice1) => {
    if (isUpdatingOrders) {
      console.log('⚠️ 正在更新档位数据，跳过重复生成')
      return
    }

    isUpdatingOrders = true
    console.log('🔄 根据买一价和卖一价生成档位数据:', {
      bidPrice1,
      askPrice1,
      upperLimit: upperLimitPrice.value,
      lowerLimit: lowerLimitPrice.value
    })

    try {
      const priceStep = PRICE_LEVELS.value.PRICE_STEP

      if (!bidPrice1 || !askPrice1 || bidPrice1 <= 0 || askPrice1 <= 0) {
        console.warn('⚠️ 买一价或卖一价无效，跳过价格生成')
        return
      }

      const roundedBidPrice1 = Math.round(bidPrice1)
      const roundedAskPrice1 = Math.round(askPrice1)

      // 根据价格间隔计算表格行数
      const maxPrice = upperLimitPrice.value > 0 ? upperLimitPrice.value : roundedAskPrice1 + 50 * priceStep
      const minPrice = lowerLimitPrice.value > 0 ? lowerLimitPrice.value : roundedBidPrice1 - 50 * priceStep

      // 计算卖盘行数：从卖一价到涨停价的间隔数
      const sellLevels = Math.max(1, Math.floor((maxPrice - roundedAskPrice1) / priceStep) + 1)

      // 计算买盘行数：从买一价到跌停价的间隔数
      const buyLevels = Math.max(1, Math.floor((roundedBidPrice1 - minPrice) / priceStep) + 1)

      console.log('📊 根据价格间隔计算表格行数:', {
        卖一价: roundedAskPrice1,
        涨停价: maxPrice,
        卖盘行数: sellLevels,
        买一价: roundedBidPrice1,
        跌停价: minPrice,
        买盘行数: buyLevels,
        价格步长: priceStep
      })

      // 更新PRICE_LEVELS配置
      PRICE_LEVELS.value.SELL_LEVELS = sellLevels
      PRICE_LEVELS.value.BUY_LEVELS = buyLevels

      // 生成完整的价格序列
      const allPrices = []

      // 1. 生成卖盘价格：从卖一价往上生成，直到涨停价
      let currentPrice = roundedAskPrice1
      let sellCount = 0

      while (sellCount < sellLevels && currentPrice <= maxPrice) {
        allPrices.push({
          price: currentPrice,
          type: 'sell',
          color: getPriceBackgroundColor(currentPrice, bidPrice1, askPrice1)
        })
        currentPrice += priceStep
        sellCount++
      }

      // 2. 补全买一价和卖一价之间的间隔（如果存在）
      if (roundedAskPrice1 - roundedBidPrice1 > priceStep) {
        let gapPrice = roundedBidPrice1 + priceStep
        while (gapPrice < roundedAskPrice1) {
          allPrices.push({
            price: gapPrice,
            type: 'gap',
            color: getPriceBackgroundColor(gapPrice, bidPrice1, askPrice1)
          })
          gapPrice += priceStep
        }
      }

      // 3. 生成买盘价格：从买一价往下生成，直到跌停价
      currentPrice = roundedBidPrice1
      let buyCount = 0

      while (buyCount < buyLevels && currentPrice >= minPrice) {
        allPrices.push({
          price: currentPrice,
          type: 'buy',
          color: getPriceBackgroundColor(currentPrice, bidPrice1, askPrice1)
        })
        currentPrice -= priceStep
        buyCount++
      }

      // 按价格从高到低排序
      allPrices.sort((a, b) => b.price - a.price)

      // 分离卖盘和买盘数据，并添加市场数据
      const newSellOrders = []
      const newBuyOrders = []
      const gapOrders = []

      allPrices.forEach((item, index) => {
        const marketData = marketDataMap.value.get(item.price)

        // 根据价格位置决定是否显示买量和卖量
        // 买量：只在买一价及以下区域显示
        // 卖量：只在卖一价及以上区域显示
        const showBuyVolume = item.price <= roundedBidPrice1
        const showSellVolume = item.price >= roundedAskPrice1

        // 获取我的挂单数量（如果需要合并显示）
        // 注意：这需要从PriceTable组件获取挂单数据
        const myBuyVolume = 0  // 这里可以获取我的买单数量
        const mySellVolume = 0 // 这里可以获取我的卖单数量

        const orderData = {
          price: item.price,
          // 选项1：只显示市场数据（当前实现）
          buyVolume: showBuyVolume ? (marketData?.bidVolume || 0) : 0,
          sellVolume: showSellVolume ? (marketData?.askVolume || 0) : 0,

          // 选项2：显示市场数据+我的挂单（如果需要）
          // buyVolume: showBuyVolume ? ((marketData?.bidVolume || 0) + myBuyVolume) : 0,
          // sellVolume: showSellVolume ? ((marketData?.askVolume || 0) + mySellVolume) : 0,

          level: (index + 1).toString(),
          color: item.color,

          // 额外信息：分别记录市场数据和我的数据
          marketBuyVolume: showBuyVolume ? (marketData?.bidVolume || 0) : 0,
          marketSellVolume: showSellVolume ? (marketData?.askVolume || 0) : 0,
          myBuyVolume: myBuyVolume,
          mySellVolume: mySellVolume
        }

        if (item.type === 'sell') {
          newSellOrders.push(orderData)
        } else if (item.type === 'buy') {
          newBuyOrders.push(orderData)
        } else if (item.type === 'gap') {
          gapOrders.push(orderData)
        }
      })

      console.log('✅ 价格档位生成完成:', {
        卖盘档位: newSellOrders.length,
        间隔档位: gapOrders.length,
        买盘档位: newBuyOrders.length,
        买一价: roundedBidPrice1,
        卖一价: roundedAskPrice1,
        价格间隔: roundedAskPrice1 - roundedBidPrice1,
        总价格档位: allPrices.length
      })

      // 合并所有订单数据（包括间隔价格）
      const allOrdersData = [...newSellOrders, ...gapOrders, ...newBuyOrders]

      // 按价格从高到低重新排序
      allOrdersData.sort((a, b) => b.price - a.price)

      // 根据价格位置和颜色规则处理数据（保持原有颜色，已经包含特殊价格颜色）
      const processedData = allOrdersData.map(order => ({
        ...order,
        // 使用已经计算好的颜色，包含特殊价格的颜色规则
        color: order.color
      }))

      // 分离卖盘和买盘数据（包括间隔价格）
      const sellData = processedData.filter(item => item.price >= roundedAskPrice1)
      const gapData = processedData.filter(item => item.price > roundedBidPrice1 && item.price < roundedAskPrice1)
      const buyData = processedData.filter(item => item.price <= roundedBidPrice1)

      // 更新处理后的订单簿数据
      processedOrderBook.value.sell = sellData
      processedOrderBook.value.buy = [...gapData, ...buyData] // 间隔价格归入买盘区域显示

      console.log('✅ 档位数据处理完成:', {
        卖盘数据: sellData.length,
        间隔数据: gapData.length,
        买盘数据: buyData.length,
        总数据: processedData.length
      })

      // 更新原始数据
      sellOrders.value = newSellOrders
      buyOrders.value = newBuyOrders

      console.log('✅ 档位数据生成完成:', {
        卖盘档位: newSellOrders.length,
        间隔档位: gapOrders.length,
        买盘档位: newBuyOrders.length,
        买一价: roundedBidPrice1,
        卖一价: roundedAskPrice1,
        涨停价: upperLimitPrice.value,
        跌停价: lowerLimitPrice.value,
        价格间隔: roundedAskPrice1 - roundedBidPrice1 > priceStep ? '存在间隔' : '无间隔'
      })
    } finally {
      isUpdatingOrders = false
    }
  }

  return {
    // 状态
    isUsingRealData,
    currentPrice,
    priceDirection,
    priceChangePercent,
    totalVolume,
    totalPosition,
    dailyPositionChange,
    redValue,
    blueValue,
    upperLimitPrice,
    lowerLimitPrice,
    openPrice,
    highestPrice,
    lowestPrice,
    bid1Price,
    ask1Price,
    sellOrders,
    buyOrders,
    marketDataMap,
    processedOrderBook,
    PRICE_LEVELS,
    currentSubscribedContract,
    panelInstanceId,
    lastUpdateTime,
    lastUpdateTimestamp,

    // 方法
    calculateTableRows,
    validatePriceContinuity,
    fillPriceGaps,
    getPriceColor,
    getPriceBackgroundColor,
    renderOrderBook,
    generateEmptyPriceOrders,
    updateCurrentPrice,
    cleanupCurrentSubscription,
    updateMarketData,
    queryMarketData,
    generatePriceOrders,
    generateDefaultPriceTable,

    // 常量
    PriceColor
  }
}
