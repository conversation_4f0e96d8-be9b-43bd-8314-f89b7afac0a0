// 声音管理组合式函数
import { ref, onMounted, onUnmounted } from 'vue'

export function useSoundManager() {
  // 音频对象缓存
  const audioCache = new Map<string, HTMLAudioElement>()
  
  // 定时器引用
  const marketOpenTimer = ref<NodeJS.Timeout | null>(null)
  const marketCloseTimer = ref<NodeJS.Timeout | null>(null)
  const marketCloseIntervalTimer = ref<NodeJS.Timeout | null>(null)

  // 音频文件路径映射
  const soundFiles = {
    fiveTimes: '/video/fiveTimes.wav',      // 开盘前5分钟
    longOnce: '/video/longOnce.wav',        // 开盘前1分钟 & 收盘前1分钟
    placeOrder: '/video/placeOrder.wav',    // 下单成功
    cancelOrder: '/video/cancelOrder.wav',  // 撤单成功
    success: '/video/sucess.wav'            // 成交成功
  }

  // 预加载音频文件
  const preloadAudio = () => {
    Object.entries(soundFiles).forEach(([key, path]) => {
      const audio = new Audio(path)
      audio.preload = 'auto'
      audioCache.set(key, audio)
    })
  }

  // 播放声音
  const playSound = (soundType: keyof typeof soundFiles) => {
    try {
      const audio = audioCache.get(soundType)
      if (audio) {
        // 重置播放位置
        audio.currentTime = 0
        audio.play().catch(error => {
          console.warn(`播放声音失败 ${soundType}:`, error)
        })
      } else {
        console.warn(`未找到声音文件: ${soundType}`)
      }
    } catch (error) {
      console.error(`播放声音出错 ${soundType}:`, error)
    }
  }

  // 获取当前时间的小时和分钟
  const getCurrentTime = () => {
    const now = new Date()
    return {
      hours: now.getHours(),
      minutes: now.getMinutes(),
      seconds: now.getSeconds()
    }
  }

  // 计算到指定时间的毫秒数
  const getTimeUntil = (targetHours: number, targetMinutes: number, targetSeconds: number = 0) => {
    const now = new Date()
    const target = new Date()
    target.setHours(targetHours, targetMinutes, targetSeconds, 0)
    
    // 如果目标时间已过，设置为明天
    if (target <= now) {
      target.setDate(target.getDate() + 1)
    }
    
    return target.getTime() - now.getTime()
  }

  // 设置开盘提醒
  const setupMarketOpenReminders = () => {
    // 清除现有定时器
    if (marketOpenTimer.value) {
      clearTimeout(marketOpenTimer.value)
    }

    // 开盘前5分钟提醒 (8:55)
    const timeUntilFiveMinutes = getTimeUntil(8, 55)
    setTimeout(() => {
      playSound('fiveTimes')
      console.log('🔔 开盘前5分钟提醒')
    }, timeUntilFiveMinutes)

    // 开盘前1分钟提醒 (8:59)
    const timeUntilOneMinute = getTimeUntil(8, 59)
    setTimeout(() => {
      playSound('longOnce')
      console.log('🔔 开盘前1分钟提醒')
    }, timeUntilOneMinute)

    console.log(`🔔 开盘提醒已设置: 5分钟提醒在${Math.round(timeUntilFiveMinutes/1000/60)}分钟后, 1分钟提醒在${Math.round(timeUntilOneMinute/1000/60)}分钟后`)
  }

  // 设置收盘提醒
  const setupMarketCloseReminders = () => {
    // 清除现有定时器
    if (marketCloseTimer.value) {
      clearTimeout(marketCloseTimer.value)
    }
    if (marketCloseIntervalTimer.value) {
      clearInterval(marketCloseIntervalTimer.value)
    }

    // 收盘前1分钟开始，每5秒响一次 (14:59开始)
    const timeUntilCloseReminder = getTimeUntil(14, 59)
    marketCloseTimer.value = setTimeout(() => {
      // 立即播放一次
      playSound('longOnce')
      console.log('🔔 收盘前1分钟提醒开始')

      // 然后每5秒播放一次，直到15:15
      marketCloseIntervalTimer.value = setInterval(() => {
        const { hours, minutes } = getCurrentTime()

        // 如果已经过了15:15，停止提醒
        if (hours > 15 || (hours === 15 && minutes >= 15)) {
          if (marketCloseIntervalTimer.value) {
            clearInterval(marketCloseIntervalTimer.value)
            marketCloseIntervalTimer.value = null
          }
          console.log('🔔 收盘提醒结束')
          return
        }

        playSound('longOnce')
        console.log('🔔 收盘提醒')
      }, 5000) // 每5秒
    }, timeUntilCloseReminder)

    console.log(`🔔 收盘提醒已设置: 在${Math.round(timeUntilCloseReminder/1000/60)}分钟后开始`)
  }

  // 初始化声音管理器
  const initSoundManager = () => {
    preloadAudio()
    setupMarketOpenReminders()
    setupMarketCloseReminders()
    
    // 每天重新设置提醒（在午夜后重新设置）
    const timeUntilMidnight = getTimeUntil(0, 0, 1) // 午夜后1秒
    setTimeout(() => {
      setupMarketOpenReminders()
      setupMarketCloseReminders()
      
      // 设置每日重复
      setInterval(() => {
        setupMarketOpenReminders()
        setupMarketCloseReminders()
      }, 24 * 60 * 60 * 1000) // 每24小时
    }, timeUntilMidnight)
  }

  // 交易相关声音
  const playPlaceOrderSound = () => playSound('placeOrder')
  const playCancelOrderSound = () => playSound('cancelOrder')
  const playSuccessSound = () => playSound('success')

  // 测试所有声音
  const testAllSounds = () => {
    console.log('🔊 测试所有声音...')
    setTimeout(() => {
      playSound('fiveTimes')
      console.log('🔊 播放: 开盘前5分钟提醒')
    }, 0)
    setTimeout(() => {
      playSound('longOnce')
      console.log('🔊 播放: 开盘前1分钟/收盘前提醒')
    }, 1000)
    setTimeout(() => {
      playSound('placeOrder')
      console.log('🔊 播放: 下单成功')
    }, 2000)
    setTimeout(() => {
      playSound('cancelOrder')
      console.log('🔊 播放: 撤单成功')
    }, 3000)
    setTimeout(() => {
      playSound('success')
      console.log('🔊 播放: 成交成功')
    }, 4000)
  }

  // 测试开盘提醒（立即触发）
  const testMarketOpenReminders = () => {
    console.log('🔊 测试开盘提醒...')
    setTimeout(() => {
      playSound('fiveTimes')
      console.log('🔔 模拟开盘前5分钟提醒')
    }, 0)
    setTimeout(() => {
      playSound('longOnce')
      console.log('🔔 模拟开盘前1分钟提醒')
    }, 2000)
  }

  // 测试收盘提醒（立即触发）
  const testMarketCloseReminders = () => {
    console.log('🔊 测试收盘提醒...')
    let count = 0
    const interval = setInterval(() => {
      playSound('longOnce')
      count++
      console.log(`🔔 模拟收盘前提醒 (${count}/3)`)
      if (count >= 3) {
        clearInterval(interval)
        console.log('🔔 收盘提醒测试结束')
      }
    }, 1000)
  }

  // 清理定时器
  const cleanup = () => {
    if (marketOpenTimer.value) {
      clearTimeout(marketOpenTimer.value)
      marketOpenTimer.value = null
    }
    if (marketCloseTimer.value) {
      clearTimeout(marketCloseTimer.value)
      marketCloseTimer.value = null
    }
    if (marketCloseIntervalTimer.value) {
      clearInterval(marketCloseIntervalTimer.value)
      marketCloseIntervalTimer.value = null
    }
  }

  // 组件挂载时初始化
  onMounted(() => {
    initSoundManager()
  })

  // 组件卸载时清理
  onUnmounted(() => {
    cleanup()
  })

  return {
    playSound,
    playPlaceOrderSound,
    playCancelOrderSound,
    playSuccessSound,
    testAllSounds,
    testMarketOpenReminders,
    testMarketCloseReminders,
    initSoundManager,
    cleanup
  }
}
