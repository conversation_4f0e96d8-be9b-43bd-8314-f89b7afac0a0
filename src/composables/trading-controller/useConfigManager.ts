// 配置管理组合式函数

import { ref } from 'vue'
import type { PanelConfig, TradingState, MarketData } from '@/types/trading'
import { WebviewWindow } from '@tauri-apps/api/webviewWindow'
import { emit, listen } from '@tauri-apps/api/event'
import { UserStorageService } from '../../services/userStorage'

export function useConfigManager() {
  const activeSet = ref(1)

  // 从面板ID中提取合约代码的辅助函数
  const getContractCodeFromPanelId = (panelId: string): string | null => {
    // 尝试从合约存储中获取面板对应的合约信息
    try {
      // 从localStorage中获取面板合约映射信息
      const panelContractsData = localStorage.getItem('panel_contracts')
      if (panelContractsData) {
        const panelContracts = JSON.parse(panelContractsData)
        return panelContracts[panelId]?.code || null
      }
      return null
    } catch (error) {
      console.warn('获取面板合约代码失败:', error)
      return null
    }
  }

  // 从交易面板获取实际配置数据
  const getPanelConfiguration = async (panelId: string, panel: WebviewWindow): Promise<any> => {
    return new Promise((resolve, reject) => {
      let isResolved = false
      let unlistenFn: (() => void) | null = null
      
      const timeout = setTimeout(() => {
        if (!isResolved) {
          isResolved = true
          if (unlistenFn) {
            unlistenFn()
          }
          reject(new Error(`获取面板${panelId}配置超时`))
        }
      }, 5000) // 5秒超时

      // 监听面板配置响应
      listen(`panel-config-response-${panelId}`, (event) => {
        if (!isResolved) {
          isResolved = true
          clearTimeout(timeout)
          if (unlistenFn) {
            unlistenFn()
          }
          resolve(event.payload)
        }
      }).then(fn => {
        unlistenFn = fn
      }).catch(error => {
        if (!isResolved) {
          isResolved = true
          clearTimeout(timeout)
          reject(error)
        }
      })

      // 向面板发送获取配置请求
      emit(`get-panel-config-${panelId}`, { requestId: panelId })
        .catch(error => {
          if (!isResolved) {
            isResolved = true
            clearTimeout(timeout)
            if (unlistenFn) {
              unlistenFn()
            }
            reject(error)
          }
        })
    })
  }

  // 保存配置到XML
  const saveConfiguration = async (tradingPanels: Map<string, WebviewWindow>) => {
    try {
      // 收集所有面板的窗口信息和状态
      const panelConfigs: PanelConfig[] = []

      for (const [panelId, panel] of Array.from(tradingPanels.entries())) {
        if (panel) {
          try {
            // 解析面板ID获取集合编号
            const setNumber = parseInt(panelId.split('-')[2])

            // 获取面板的合约代码
            const contractCode = getContractCodeFromPanelId(panelId)
            const panelTitle = contractCode || `交易面板 - 集合${setNumber}`

            // 获取面板的实际配置数据（包含窗口信息）
            let tradingState: TradingState, marketData: MarketData, windowInfo: any
            try {
              console.log(`正在获取面板${panelId}的配置数据...`)
              const panelConfig = await getPanelConfiguration(panelId, panel)
              tradingState = panelConfig.tradingState
              marketData = panelConfig.marketData
              windowInfo = panelConfig.window // 从面板获取的窗口信息
              console.log(`成功获取面板${panelId}的配置:`, panelConfig)
            } catch (error) {
              console.warn(`获取面板${panelId}配置失败，使用默认值:`, error)
              // 使用默认值作为后备
              tradingState = {
                currentPrice: 0,
                fontSize: 11,
                cellHeight: 18,
                orderType: 'A',
                lightOrderQuantity: 1,
                heavyOrderQuantity: 5,
                positionMode: 'open',
                cancelMode: 'limited',
                maxCancelOrders: 489,
                currentCancelCount: 0
              }
              marketData = {
                totalVolume: 0,
                totalPosition: 0,
                dailyPositionChange: 0,
                priceChangePercent: 0
              }
            }

            // 面板配置
            const panelConfig: PanelConfig = {
              id: panelId,
              setNumber: setNumber,
              title: panelTitle,
              window: windowInfo,
              tradingState: tradingState,
              marketData: marketData,
              timestamp: new Date().toISOString()
            }

            panelConfigs.push(panelConfig)
          } catch (error) {
            console.error(`获取面板${panelId}信息失败:`, error)
          }
        }
      }

      // 生成XML内容
      const xmlContent = await generateTradingPanelXML(panelConfigs)

      // 保存到文件
      await saveTradingPanelXML(xmlContent)

      // 同时保存到localStorage作为备份
      const config = {
        activeSet: activeSet.value,
        panelConfigs: panelConfigs,
        timestamp: new Date().toISOString()
      }
      localStorage.setItem('trading_controller_config', JSON.stringify(config))

      console.log('交易面板配置已保存到XML文件')
    } catch (error) {
      console.error('保存配置失败:', error)
    }
  }

  // 生成交易面板XML内容 - 按照用户指定的格式
  const generateTradingPanelXML = async (panelConfigs: PanelConfig[]) => {
    // 获取用户账号信息
    const userInfo = UserStorageService.getUserInfo()
    const accountNumber = userInfo?.account || '********' // 默认账号

    let xml = `<?xml version="1.0" encoding="UTF-8"?>
<a>`

    // 使用 for...of 循环来支持异步操作
    for (const config of panelConfigs) {
      // 从面板配置中获取合约代码
      const contractCode = getContractCodeFromPanelId(config.id) || config.title.replace('交易面板 - 集合', 'contract')

      // 计算金手指值 (FZ) - 根据交易状态中的选项计算
      const fzValue = calculateFingerValue(config.tradingState)

      // 获取轻仓重仓数量
      const lightQuantity = config.tradingState.lightOrderQuantity || 1
      const heavyQuantity = config.tradingState.heavyOrderQuantity || 5

      // 判断多模式还是单模式 (MS) - 根据订单类型判断
      const modeValue = config.tradingState.orderType === 'A' ? 'A' : 'B'

      // 屏幕编号 (SC) - 根据窗口位置计算
      const screenNumber = await calculateScreenNumber(config.window.x, config.window.y)

      xml += `
    <a${accountNumber} Title="${contractCode}" X="${config.window.x}" Y="${config.window.y}" Width="${config.window.width}" Height="${config.window.height}" RowHeight="${config.tradingState.cellHeight || 16}" FZ="${fzValue}" Z_NUM="${lightQuantity}" Y_NUM="${heavyQuantity}" MS="${modeValue}" SC="${screenNumber}" PID="${config.id}" />`
    }

    xml += `
</a>`

    return xml
  }



  // 计算金手指值的辅助函数
  const calculateFingerValue = (_tradingState: TradingState): number => {
    // 根据交易状态中的选项计算金手指值
    // 这里可以根据具体的业务逻辑来计算
    // 暂时返回一个默认值，可以根据实际需求调整
    return 1 // 默认金手指值
  }

  // 计算屏幕编号的辅助函数（异步版本）
  const calculateScreenNumber = async (x: number, y: number): Promise<number> => {
    try {
      // 使用Tauri API获取所有监视器信息
      const { availableMonitors, monitorFromPoint } = await import('@tauri-apps/api/window')

      // 尝试根据窗口位置找到对应的监视器
      const monitor = await monitorFromPoint(x, y)
      if (monitor) {
        // 获取所有监视器列表
        const monitors = await availableMonitors()

        // 找到当前监视器在列表中的索引，作为屏幕编号
        const screenIndex = monitors.findIndex(m =>
          m.position.x === monitor.position.x &&
          m.position.y === monitor.position.y &&
          m.size.width === monitor.size.width &&
          m.size.height === monitor.size.height
        )

        if (screenIndex >= 0) {
          return screenIndex + 1 // 屏幕编号从1开始
        }
      }

      // 如果无法确定，使用简单的计算方法作为后备
      console.warn(`无法确定窗口(${x}, ${y})所在的屏幕，使用简单计算方法`)
      return calculateScreenNumberFallback(x, y)
    } catch (error) {
      console.error('获取屏幕信息失败:', error)
      return calculateScreenNumberFallback(x, y)
    }
  }

  // 简单的屏幕编号计算方法（后备方案）
  const calculateScreenNumberFallback = (x: number, _y: number): number => {
    // 根据窗口位置计算屏幕编号
    // 假设屏幕从左到右排列，每个屏幕宽度为1920px
    const screenWidth = 1920
    const screenNumber = Math.floor(x / screenWidth) + 1
    return Math.max(1, screenNumber)
  }

  // 保存XML到文件
  const saveTradingPanelXML = async (xmlContent: string) => {
    try {
      const { invoke } = await import('@tauri-apps/api/core')

      // 生成默认文件名
      const fileName = `td_config.xml`

      // 使用当前工作目录保存文件
      const filePath = `./${fileName}`

      // 使用Tauri的write_file命令写入文件
      await invoke('write_file', {
        path: filePath,
        content: xmlContent
      })

      console.log('XML配置文件已保存到:', filePath)
      return filePath
    } catch (error) {
      console.error('保存XML文件失败:', error)
      throw error
    }
  }

  // 加载配置
  const loadConfiguration = () => {
    try {
      const saved = localStorage.getItem('trading_controller_config')
      if (saved) {
        const config = JSON.parse(saved)
        activeSet.value = config.activeSet || 1
        console.log('配置加载成功:', config)
      }
    } catch (error) {
      console.error('加载配置失败:', error)
    }
  }

  return {
    activeSet,
    saveConfiguration,
    loadConfiguration
  }
}
