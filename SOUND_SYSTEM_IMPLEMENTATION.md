# 交易面板声音系统实现

## 功能概述

本系统实现了交易面板的声音提醒功能，包括：

1. **开盘提醒**：每天9点开盘前5分钟和1分钟的声音提醒
2. **收盘提醒**：每天下午3点15分前1分钟开始，每5秒响一次的声音提醒
3. **交易声音**：下单成功、撤单成功、成交成功的声音提醒

## 声音文件

声音文件位于 `public/video/` 目录下：

- `fiveTimes.wav` - 开盘前5分钟提醒
- `longOnce.wav` - 开盘前1分钟提醒 & 收盘前提醒
- `placeOrder.wav` - 下单成功提醒
- `cancelOrder.wav` - 撤单成功提醒
- `sucess.wav` - 成交成功提醒

## 核心组件

### 1. useSoundManager.ts

声音管理的核心组合式函数，提供以下功能：

- **音频预加载**：应用启动时预加载所有音频文件
- **定时提醒**：自动设置开盘和收盘提醒定时器
- **交易声音**：提供下单、撤单、成交的声音播放接口
- **测试功能**：提供声音测试功能

### 2. 集成点

#### App.vue
- 在应用级别初始化声音管理器
- 确保声音功能在应用启动时就开始工作

#### TradingPanel.vue
- 在下单成功时播放下单声音
- 在撤单成功时播放撤单声音
- 在成交通知时播放成交声音

#### TradingController.vue
- 提供声音测试按钮
- 集成声音测试功能

## 时间安排

### 开盘提醒
- **8:55** - 播放 `fiveTimes.wav`（开盘前5分钟）
- **8:59** - 播放 `longOnce.wav`（开盘前1分钟）

### 收盘提醒
- **14:59** - 开始播放 `longOnce.wav`
- **14:59-15:15** - 每5秒播放一次 `longOnce.wav`

### 交易声音
- **下单成功** - 播放 `placeOrder.wav`
- **撤单成功** - 播放 `cancelOrder.wav`
- **成交成功** - 播放 `sucess.wav`

## 测试功能

在交易控制器界面提供了三个测试按钮：

1. **🔊** - 测试所有声音（依次播放所有声音文件）
2. **🌅** - 测试开盘提醒（模拟开盘前5分钟和1分钟提醒）
3. **🌇** - 测试收盘提醒（模拟收盘前提醒，播放3次）

## 技术实现

### 音频管理
- 使用 HTML5 Audio API
- 音频文件预加载到内存中
- 支持重复播放和重置播放位置

### 定时器管理
- 使用 setTimeout 和 setInterval 管理定时提醒
- 自动计算到目标时间的毫秒数
- 支持跨日期的时间计算

### 错误处理
- 音频播放失败时的错误捕获
- 文件加载失败的容错处理
- 定时器清理和内存管理

## 使用方法

### 基本使用
```typescript
import { useSoundManager } from '@/composables/useSoundManager'

const { 
  playPlaceOrderSound, 
  playCancelOrderSound, 
  playSuccessSound 
} = useSoundManager()

// 播放下单成功声音
playPlaceOrderSound()

// 播放撤单成功声音
playCancelOrderSound()

// 播放成交成功声音
playSuccessSound()
```

### 测试功能
```typescript
const { 
  testAllSounds, 
  testMarketOpenReminders, 
  testMarketCloseReminders 
} = useSoundManager()

// 测试所有声音
testAllSounds()

// 测试开盘提醒
testMarketOpenReminders()

// 测试收盘提醒
testMarketCloseReminders()
```

## 注意事项

1. **浏览器限制**：现代浏览器要求用户交互后才能播放音频，建议在用户首次点击后初始化
2. **文件路径**：音频文件必须放在 `public/video/` 目录下
3. **性能考虑**：音频文件已预加载，避免播放时的延迟
4. **内存管理**：组件卸载时会自动清理定时器和音频资源

## 扩展功能

系统设计为可扩展的，可以轻松添加：
- 新的声音类型
- 自定义提醒时间
- 音量控制
- 声音开关设置
- 不同合约的个性化声音
