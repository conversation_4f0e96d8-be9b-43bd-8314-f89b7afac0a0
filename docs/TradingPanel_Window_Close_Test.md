# TradingPanel 窗口关闭时移除合约行情数据获取 - 测试指南

## 功能概述

当 TradingPanel 窗口关闭时，系统会自动：
1. **阻止窗口立即关闭** - 使用 `event.preventDefault()` 阻止窗口立即关闭
2. **立即移除监听器** - 防止继续接收行情数据
3. **等待取消订阅完成** - 使用超时机制确保操作完成
4. **手动关闭窗口** - 清理完成后主动关闭窗口
5. **提供用户反馈** - 显示清理状态信息

## 核心改进

### 🔧 解决的关键问题
- **问题**：调用 `unsubscribeMarketData` 时，窗口在接口返回前就关闭了
- **解决方案**：使用 `event.preventDefault()` 阻止窗口关闭，等待清理完成后手动关闭

## 测试步骤

### 1. 正常窗口关闭测试

**步骤：**
1. 打开一个 TradingPanel 窗口
2. 确保已订阅某个合约的行情数据（如 rb2509）
3. 观察控制台日志，确认行情数据正在接收
4. 点击窗口的关闭按钮（X）

**预期结果：**
```
🔄 检测到窗口关闭请求，阻止关闭并开始清理行情数据...
⏳ 正在等待行情数据清理完成...
🚨 窗口关闭强制清理开始...
✅ 强制移除行情数据监听器
🔄 强制取消订阅合约行情: ["rb2509"]
✅ 窗口关闭前成功取消订阅: ["rb2509"]
✅ 窗口关闭强制清理完成
✅ 窗口关闭前行情数据清理完成
🔄 行情数据清理完成，现在关闭窗口...
```

**用户界面反馈：**
- 显示成功消息：`窗口关闭：已断开 rb2509 行情连接` (2秒显示)

**超时情况：**
```
🔄 检测到窗口关闭请求，阻止关闭并开始清理行情数据...
⏳ 正在等待行情数据清理完成...
🚨 窗口关闭强制清理开始...
✅ 强制移除行情数据监听器
🔄 强制取消订阅合约行情: ["rb2509"]
❌ 窗口关闭前取消订阅异常: Error: 窗口关闭超时
⚠️ 窗口关闭超时，强制清理本地状态
✅ 窗口关闭强制清理完成
✅ 窗口关闭前行情数据清理完成
🔄 行情数据清理完成，现在关闭窗口...
```

**用户界面反馈（超时）：**
- 显示警告消息：`行情断开超时，已强制清理` (2秒显示)

### 2. 合约切换测试

**步骤：**
1. 打开 TradingPanel 窗口
2. 订阅合约 A 的行情（如 rb2509）
3. 切换到合约 B（如 au2506）
4. 观察控制台日志

**预期结果：**
```
🔄 检测到合约变化: { 从: "rb2509", 到: "au2506" }
🧹 开始清理行情订阅: {
  监听器存在: true,
  订阅合约数量: 1,
  订阅合约列表: ["rb2509"],
  显示用户消息: false
}
✅ 已移除行情数据监听器
🔄 正在取消订阅合约行情: ["rb2509"]
✅ 成功取消订阅所有合约行情: ["rb2509"]
🔍 查询行情数据: au2506
✅ 行情数据订阅成功: ...
```

### 3. 多合约订阅测试

**步骤：**
1. 修改代码以支持多合约订阅（测试用）
2. 订阅多个合约的行情
3. 关闭窗口

**预期结果：**
- 所有订阅的合约都应该被正确取消订阅
- 控制台显示所有合约的清理日志

### 4. 异常情况测试

**步骤：**
1. 断开网络连接
2. 尝试关闭窗口

**预期结果：**
- 系统应该优雅地处理网络错误
- 显示适当的错误消息
- 不应该阻止窗口关闭

## 调试信息

### 关键日志标识

- `🔄 检测到窗口关闭请求` - 窗口关闭事件触发
- `🧹 开始清理行情订阅` - 清理函数开始执行
- `✅ 已移除行情数据监听器` - 事件监听器已移除
- `🔄 正在取消订阅合约行情` - 开始取消订阅
- `✅ 成功取消订阅所有合约行情` - 取消订阅成功
- `✅ 合约行情数据获取已完全移除` - 完整清理完成

### 用户消息

成功情况：
- `已断开 [合约代码] 行情数据连接`
- `已切换到 [合约名称] ([合约代码]) 行情`

失败情况：
- `断开行情连接失败: [错误信息]`
- `断开行情连接异常: [异常信息]`

## 验证要点

1. **资源完全清理**：确保没有内存泄漏
2. **网络连接断开**：确认不再接收行情数据
3. **用户反馈及时**：提供清晰的状态信息
4. **错误处理健壮**：异常情况下不影响窗口关闭
5. **日志信息完整**：便于调试和监控

## 常见问题

### Q: 窗口关闭后仍然收到行情数据？
A: 检查 `marketDataListener` 是否正确移除，确认 `ctpService.off()` 调用成功

### Q: 取消订阅失败？
A: 检查网络连接和 CTP 服务状态，查看具体错误信息

### Q: 窗口关闭被阻止？
A: 确认 `onCloseRequested` 回调中没有调用 `event.preventDefault()`

### Q: 清理函数执行两次？
A: 这是正常的，`onCloseRequested` 和 `onUnmounted` 都会触发清理，但函数设计为幂等的

## 性能影响

- 清理操作通常在 100-500ms 内完成
- 网络请求可能需要额外时间
- 不会显著影响窗口关闭速度
