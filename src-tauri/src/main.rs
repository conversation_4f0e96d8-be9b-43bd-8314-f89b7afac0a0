// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]
mod file_io;
mod ctp_commands;

#[tokio::main]
async fn main() {
    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![
            file_io::read_file,
            file_io::write_file,
            ctp_commands::get_api_version,
            ctp_commands::create_md_api,
            ctp_commands::release_md_api,
            ctp_commands::create_trader_api,
            ctp_commands::release_trader_api,
            ctp_commands::md_login,
            ctp_commands::trader_login,
            ctp_commands::subscribe_market_data,
            ctp_commands::unsubscribe_market_data,
            ctp_commands::insert_order,
            ctp_commands::cancel_order,
            ctp_commands::get_cancel_order_result,
            ctp_commands::query_account,
            ctp_commands::query_position,
            ctp_commands::query_instruments,
            ctp_commands::validate_trader_session,
            ctp_commands::get_active_trader_session,
            ctp_commands::test_order_interface,
            ctp_commands::check_ctp_connection_status,
            ctp_commands::force_set_trader_login_status,
            ctp_commands::get_session_info,
            ctp_commands::query_all_orders,
            ctp_commands::query_order_remaining_volume,
            ctp_commands::query_all_remaining_volumes,
            ctp_commands::check_settlement_confirm_status,
            ctp_commands::settlement_info_confirm,
            ctp_commands::diagnose_order_fields,
            ctp_commands::check_order_insert_result,
            ctp_commands::analyze_order_rejection,
            ctp_commands::check_order_submission_status,
            // ctp_commands::cleanup_current_dir_cache_command,
        ])
        .setup(|app| {
            // 设置全局App Handle用于发送事件
            ctp_commands::set_app_handle(app.handle().clone());
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
