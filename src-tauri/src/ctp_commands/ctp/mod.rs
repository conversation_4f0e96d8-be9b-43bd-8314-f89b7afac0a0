// CTP 模块入口文件
// 重构后的 CTP 功能模块化组织

pub mod types;
pub mod state;
pub mod utils;
pub mod api_manager;
pub mod market_data;
pub mod trading;
pub mod auth;
pub mod query;
pub mod event_handler;
pub mod diagnostics;
// pub mod cache_manager; // 临时屏蔽缓存管理器模块

// 重新导出所有公共接口
pub use api_manager::*;
pub use market_data::*;
pub use trading::*;
pub use auth::*;
pub use query::*;
pub use diagnostics::*;
// pub use cache_manager::*; // 临时屏蔽缓存管理器导出

// 导出状态管理函数
pub use state::set_app_handle;

