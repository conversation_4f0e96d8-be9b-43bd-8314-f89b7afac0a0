use tauri::{App<PERSON><PERSON><PERSON>};
use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use crate::ctp_commands::ctp::types::{CtpAccountConfig, InstrumentInfo, OrderInfo, PositionInfo};

// 全局状态管理
lazy_static::lazy_static! {
    static ref MD_APIS: Arc<Mutex<HashMap<String, Box<tauri_app_vue_lib::CThostFtdcMdApi>>>> =
        Arc::new(Mutex::new(HashMap::new()));
    static ref TRADER_APIS: Arc<Mutex<HashMap<String, Box<tauri_app_vue_lib::CThostFtdcTraderApi>>>> =
        Arc::new(Mutex::new(HashMap::new()));
    // 存储MD API的Stream
    static ref MD_STREAMS: Arc<Mutex<HashMap<String, Box<tauri_app_vue_lib::md_api::CThostFtdcMdSpiStream>>>> =
        Arc::new(Mutex::new(HashMap::new()));
    // 存储Trader API的Stream
    static ref TRADER_STREAMS: Arc<Mutex<HashMap<String, Box<tauri_app_vue_lib::trader_api::CThostFtdcTraderSpiStream>>>> =
        Arc::new(Mutex::new(HashMap::new()));
    // 跟踪交易API的登录状态
    static ref TRADER_LOGIN_STATUS: Arc<Mutex<HashMap<String, bool>>> =
        Arc::new(Mutex::new(HashMap::new()));
    // 跟踪行情API的登录状态
    static ref MD_LOGIN_STATUS: Arc<Mutex<HashMap<String, bool>>> =
        Arc::new(Mutex::new(HashMap::new()));
    // 存储登录结果信息 (session_id -> (success, message))
    static ref LOGIN_RESULTS: Arc<Mutex<HashMap<String, (bool, String)>>> =
        Arc::new(Mutex::new(HashMap::new()));
    // 全局请求ID计数器
    static ref REQUEST_ID_COUNTER: Arc<Mutex<i32>> = Arc::new(Mutex::new(1));
    // 全局订单引用计数器
    static ref ORDER_REF_COUNTER: Arc<Mutex<i32>> = Arc::new(Mutex::new(1));
    // 存储订单插入结果 (order_ref -> (success, message))
    static ref ORDER_INSERT_RESULTS: Arc<Mutex<HashMap<String, (bool, String)>>> =
        Arc::new(Mutex::new(HashMap::new()));
    // 存储会话的登录信息
    static ref SESSION_LOGIN_INFO: Arc<Mutex<HashMap<String, CtpAccountConfig>>> =
        Arc::new(Mutex::new(HashMap::new()));
    // 存储结算确认结果 (session_id -> (success, message))
    static ref SETTLEMENT_CONFIRM_RESULTS: Arc<Mutex<HashMap<String, (bool, String)>>> =
        Arc::new(Mutex::new(HashMap::new()));
    // 跟踪结算确认状态
    static ref SETTLEMENT_CONFIRM_STATUS: Arc<Mutex<HashMap<String, bool>>> =
        Arc::new(Mutex::new(HashMap::new()));
    // 存储订单的FrontID和SessionID (order_ref -> (front_id, session_id))
    static ref ORDER_FRONT_SESSION_MAP: Arc<Mutex<HashMap<String, (i32, i32)>>> =
        Arc::new(Mutex::new(HashMap::new()));
    // 存储查询到的订单信息
    static ref QUERIED_ORDERS: Arc<Mutex<HashMap<String, Vec<OrderInfo>>>> =
        Arc::new(Mutex::new(HashMap::new()));
    // 存储撤单操作结果 (action_ref -> (success, message, order_ref))
    static ref ORDER_ACTION_RESULTS: Arc<Mutex<HashMap<String, (bool, String, String)>>> =
        Arc::new(Mutex::new(HashMap::new()));

    // 存储从CTP回调获取的合约数据
    static ref CTP_INSTRUMENTS_DATA: Arc<Mutex<Vec<InstrumentInfo>>> =
        Arc::new(Mutex::new(Vec::new()));

    // 合约查询状态标记
    static ref INSTRUMENTS_QUERY_COMPLETE: Arc<Mutex<bool>> =
        Arc::new(Mutex::new(false));

    // 存储从CTP回调获取的持仓数据
    static ref CTP_POSITIONS_DATA: Arc<Mutex<Vec<PositionInfo>>> =
        Arc::new(Mutex::new(Vec::new()));

    // 持仓查询状态标记
    static ref POSITIONS_QUERY_COMPLETE: Arc<Mutex<bool>> =
        Arc::new(Mutex::new(false));

    // 全局App Handle用于发送事件到前端
    static ref APP_HANDLE: Arc<Mutex<Option<AppHandle>>> =
        Arc::new(Mutex::new(None));
}

// 设置全局App Handle
pub fn set_app_handle(app_handle: AppHandle) {
    let mut handle = APP_HANDLE.lock().unwrap();
    *handle = Some(app_handle);
}

// 状态访问器函数
pub fn get_md_apis() -> &'static Arc<Mutex<HashMap<String, Box<tauri_app_vue_lib::CThostFtdcMdApi>>>> {
    &MD_APIS
}

pub fn get_trader_apis() -> &'static Arc<Mutex<HashMap<String, Box<tauri_app_vue_lib::CThostFtdcTraderApi>>>> {
    &TRADER_APIS
}

pub fn get_md_streams() -> &'static Arc<Mutex<HashMap<String, Box<tauri_app_vue_lib::md_api::CThostFtdcMdSpiStream>>>> {
    &MD_STREAMS
}

pub fn get_trader_streams() -> &'static Arc<Mutex<HashMap<String, Box<tauri_app_vue_lib::trader_api::CThostFtdcTraderSpiStream>>>> {
    &TRADER_STREAMS
}

pub fn get_trader_login_status() -> &'static Arc<Mutex<HashMap<String, bool>>> {
    &TRADER_LOGIN_STATUS
}

pub fn get_md_login_status() -> &'static Arc<Mutex<HashMap<String, bool>>> {
    &MD_LOGIN_STATUS
}

pub fn get_login_results() -> &'static Arc<Mutex<HashMap<String, (bool, String)>>> {
    &LOGIN_RESULTS
}

pub fn get_request_id_counter() -> &'static Arc<Mutex<i32>> {
    &REQUEST_ID_COUNTER
}

pub fn get_order_ref_counter() -> &'static Arc<Mutex<i32>> {
    &ORDER_REF_COUNTER
}

pub fn get_order_insert_results() -> &'static Arc<Mutex<HashMap<String, (bool, String)>>> {
    &ORDER_INSERT_RESULTS
}

pub fn get_session_login_info() -> &'static Arc<Mutex<HashMap<String, CtpAccountConfig>>> {
    &SESSION_LOGIN_INFO
}

pub fn get_settlement_confirm_results() -> &'static Arc<Mutex<HashMap<String, (bool, String)>>> {
    &SETTLEMENT_CONFIRM_RESULTS
}

pub fn get_settlement_confirm_status() -> &'static Arc<Mutex<HashMap<String, bool>>> {
    &SETTLEMENT_CONFIRM_STATUS
}

pub fn get_order_front_session_map() -> &'static Arc<Mutex<HashMap<String, (i32, i32)>>> {
    &ORDER_FRONT_SESSION_MAP
}

pub fn get_queried_orders() -> &'static Arc<Mutex<HashMap<String, Vec<OrderInfo>>>> {
    &QUERIED_ORDERS
}

pub fn get_order_action_results() -> &'static Arc<Mutex<HashMap<String, (bool, String, String)>>> {
    &ORDER_ACTION_RESULTS
}

pub fn get_ctp_instruments_data() -> &'static Arc<Mutex<Vec<InstrumentInfo>>> {
    &CTP_INSTRUMENTS_DATA
}

pub fn get_instruments_query_complete() -> &'static Arc<Mutex<bool>> {
    &INSTRUMENTS_QUERY_COMPLETE
}

pub fn get_ctp_positions_data() -> &'static Arc<Mutex<Vec<PositionInfo>>> {
    &CTP_POSITIONS_DATA
}

pub fn get_positions_query_complete() -> &'static Arc<Mutex<bool>> {
    &POSITIONS_QUERY_COMPLETE
}

pub fn get_app_handle() -> &'static Arc<Mutex<Option<AppHandle>>> {
    &APP_HANDLE
}

