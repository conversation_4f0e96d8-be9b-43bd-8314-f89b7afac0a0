use tauri::command;
use crate::ctp_commands::ctp::types::{ApiResponse, AccountInfo, PositionInfo, InstrumentInfo, OrderInfo};
use crate::ctp_commands::ctp::state::*;
use crate::ctp_commands::ctp::utils::{get_next_request_id};

// 查询账户资金
#[command]
pub fn query_account(
    session_id: String,
) -> ApiResponse<AccountInfo> {
    println!("🔍 [DEBUG] query_account called with session_id: {}", session_id);

    match std::panic::catch_unwind(|| {
        // 首先检查登录状态
        let login_status = get_trader_login_status().lock().unwrap();
        if !login_status.get(&session_id).unwrap_or(&false) {
            return Err("CTP 交易 API 未连接，请先登录".to_string());
        }
        drop(login_status); // 释放锁

        let apis = get_trader_apis().lock().unwrap();

        if let Some(_api) = apis.get(&session_id) {
            println!("✅ [DEBUG] Found Trader API for session: {}", session_id);

            // 注意：这里应该调用真实的CTP查询账户API
            // 由于当前的实现是基于异步流的，这里返回模拟数据
            let account_info = AccountInfo {
                broker_id: "9999".to_string(),
                account_id: "test_account".to_string(),
                pre_mortgage: 0.0,
                pre_credit: 0.0,
                pre_deposit: 0.0,
                pre_balance: 100000.0,
                pre_margin: 0.0,
                interest_base: 0.0,
                interest: 0.0,
                deposit: 0.0,
                withdraw: 0.0,
                frozen_margin: 0.0,
                frozen_cash: 0.0,
                frozen_commission: 0.0,
                curr_margin: 0.0,
                cash_in: 0.0,
                commission: 0.0,
                close_profit: 0.0,
                position_profit: 0.0,
                balance: 100000.0,
                available: 95000.0,
                withdraw_quota: 95000.0,
                reserve: 0.0,
                trading_day: "********".to_string(),
                settlement_id: 1,
                credit: 0.0,
                mortgage: 0.0,
                exchange_margin: 0.0,
                delivery_margin: 0.0,
                exchange_delivery_margin: 0.0,
                reserve_balance: 0.0,
                currency_id: "CNY".to_string(),
                pre_fund_mortgage_in: 0.0,
                pre_fund_mortgage_out: 0.0,
                fund_mortgage_in: 0.0,
                fund_mortgage_out: 0.0,
                fund_mortgage_available: 0.0,
                mortgage_able_fund: 0.0,
                spec_product_margin: 0.0,
                spec_product_frozen_margin: 0.0,
                spec_product_commission: 0.0,
                spec_product_frozen_commission: 0.0,
                spec_product_position_profit: 0.0,
                spec_product_close_profit: 0.0,
                spec_product_position_profit_by_alg: 0.0,
                spec_product_exchange_margin: 0.0,
                bis_margin: 0.0,
                bis_frozen_margin: 0.0,
                bis_commission: 0.0,
                bis_frozen_commission: 0.0,
                bis_position_profit: 0.0,
                bis_close_profit: 0.0,
                bis_position_profit_by_alg: 0.0,
                bis_exchange_margin: 0.0,
                frozen_swap: 0.0,
                remain_swap: 0.0,
            };

            Ok(account_info)
        } else {
            Err(format!("Session ID {} not found", session_id))
        }
    }) {
        Ok(Ok(account_info)) => {
            println!("✅ [SUCCESS] Account query successful");
            ApiResponse {
                success: true,
                data: Some(account_info),
                error: None,
            }
        },
        Ok(Err(error)) => {
            println!("❌ [ERROR] Account query failed: {}", error);
            ApiResponse {
                success: false,
                data: None,
                error: Some(error),
            }
        },
        Err(_) => {
            let error = "Account query panicked".to_string();
            println!("❌ [ERROR] {}", error);
            ApiResponse {
                success: false,
                data: None,
                error: Some(error),
            }
        }
    }
}

// 查询持仓信息
#[command]
pub async fn query_position(
    session_id: String,
) -> ApiResponse<Vec<PositionInfo>> {
    println!("🔍 [DEBUG] query_position called with session_id: {}", session_id);

    match std::panic::catch_unwind(|| {
        // 首先检查登录状态
        let login_status = get_trader_login_status().lock().unwrap();
        if !login_status.get(&session_id).unwrap_or(&false) {
            return Err("CTP 交易 API 未连接，请先登录".to_string());
        }
        drop(login_status); // 释放锁

        let mut apis = get_trader_apis().lock().unwrap();

        if let Some(api) = apis.get_mut(&session_id) {
            println!("✅ [DEBUG] Found Trader API for session: {}", session_id);

            // 真实调用CTP查询持仓接口
            use tauri_app_vue_lib::*;

            // 创建查询持仓请求结构
            let mut req = CThostFtdcQryInvestorPositionField::default();

            // 获取登录信息以填充请求字段
            let login_info = {
                let login_infos = get_session_login_info().lock().unwrap();
                login_infos.get(&session_id).cloned()
            };

            if let Some(config) = login_info {
                // 填充查询请求字段
                let broker_id_bytes = config.broker_id.as_bytes();
                let investor_id_bytes = config.account.as_bytes();

                // 复制到固定长度数组，转换u8到i8
                let broker_len = std::cmp::min(broker_id_bytes.len(), req.BrokerID.len() - 1);
                let investor_len = std::cmp::min(investor_id_bytes.len(), req.InvestorID.len() - 1);

                for i in 0..broker_len {
                    req.BrokerID[i] = broker_id_bytes[i] as i8;
                }
                for i in 0..investor_len {
                    req.InvestorID[i] = investor_id_bytes[i] as i8;
                }
            }

            // 获取唯一的请求ID
            let request_id = get_next_request_id();

            println!("🔍 [INFO] Starting real CTP position query with request_id: {}", request_id);

            // 重置查询状态
            {
                let mut query_complete = get_positions_query_complete().lock().unwrap();
                *query_complete = false;

                let mut positions_data = get_ctp_positions_data().lock().unwrap();
                positions_data.clear();
            }

            // 添加延迟以避免查询频率过快
            std::thread::sleep(std::time::Duration::from_millis(1000));

            // 调用查询持仓接口
            let result = api.req_qry_investor_position(&mut req, request_id);

            if result == 0 {
                println!("✅ [SUCCESS] CTP req_qry_investor_position called successfully, waiting for callback...");

                // 等待回调完成，设置合理的超时时间
                let mut wait_count = 0;
                let max_wait = 300; // 30秒，每次等待100ms

                println!("⏳ [INFO] Waiting for CTP position query callback...");

                loop {
                    std::thread::sleep(std::time::Duration::from_millis(100));
                    wait_count += 1;

                    let query_complete = get_positions_query_complete().lock().unwrap();
                    if *query_complete {
                        drop(query_complete);

                        let positions_data = get_ctp_positions_data().lock().unwrap();
                        let result_data = positions_data.clone();
                        drop(positions_data);

                        println!("✅ [SUCCESS] CTP position query completed, found {} real positions", result_data.len());
                        return Ok(result_data);
                    }

                    if wait_count >= max_wait {
                        println!("⏰ [TIMEOUT] CTP position query timeout after 30 seconds");
                        return Err("CTP持仓查询超时（30秒），请检查网络连接和CTP服务状态".to_string());
                    }
                }
            } else {
                println!("❌ [ERROR] CTP req_qry_investor_position failed with code: {}", result);
                return Err(format!("CTP持仓查询失败，错误代码: {}", result));
            }
        } else {
            Err(format!("Session ID {} not found", session_id))
        }
    }) {
        Ok(Ok(positions)) => {
            println!("✅ [SUCCESS] Position query initiated, {} positions", positions.len());
            ApiResponse {
                success: true,
                data: Some(positions),
                error: None,
            }
        },
        Ok(Err(error)) => {
            println!("❌ [ERROR] Position query failed: {}", error);
            ApiResponse {
                success: false,
                data: None,
                error: Some(error),
            }
        },
        Err(_) => {
            let error = "Position query panicked".to_string();
            println!("❌ [ERROR] {}", error);
            ApiResponse {
                success: false,
                data: None,
                error: Some(error),
            }
        }
    }
}

// 查询合约信息 - 完全通过真实CTP API获取
#[command]
pub async fn query_instruments(
    session_id: String,
) -> ApiResponse<Vec<InstrumentInfo>> {
    println!("🔍 [DEBUG] query_instruments called with session_id: {}", session_id);

    match std::panic::catch_unwind(|| {
        // 首先检查登录状态
        let login_status = get_trader_login_status().lock().unwrap();
        if !login_status.get(&session_id).unwrap_or(&false) {
            return Err("CTP 交易 API 未连接，请先登录".to_string());
        }
        drop(login_status); // 释放锁

        let mut apis = get_trader_apis().lock().unwrap();

        if let Some(api) = apis.get_mut(&session_id) {
            println!("✅ [DEBUG] Found Trader API for session: {}", session_id);

            // 真实调用CTP查询接口
            use tauri_app_vue_lib::*;

            // 创建查询合约请求结构
            let mut req = CThostFtdcQryInstrumentField::default();

            // 获取唯一的请求ID
            let request_id = get_next_request_id();

            println!("🔍 [INFO] Starting real CTP instrument query with request_id: {}", request_id);

            // 添加延迟以避免查询频率过快
            std::thread::sleep(std::time::Duration::from_millis(1000));

            // 调用查询合约接口
            let result = api.req_qry_instrument(&mut req, request_id);

            if result == 0 {
                println!("✅ [SUCCESS] CTP req_qry_instrument called successfully, waiting for callback...");

                // 重置查询状态
                {
                    let mut query_complete = get_instruments_query_complete().lock().unwrap();
                    *query_complete = false;

                    let mut instruments_data = get_ctp_instruments_data().lock().unwrap();
                    instruments_data.clear();
                }

                // 等待回调完成，设置合理的超时时间
                let mut wait_count = 0;
                let max_wait = 300; // 30秒，每次等待100ms

                println!("⏳ [INFO] Waiting for CTP instrument query callback...");

                loop {
                    std::thread::sleep(std::time::Duration::from_millis(100));
                    wait_count += 1;

                    let query_complete = get_instruments_query_complete().lock().unwrap();
                    if *query_complete {
                        drop(query_complete);

                        let instruments_data = get_ctp_instruments_data().lock().unwrap();
                        let result_data = instruments_data.clone();
                        drop(instruments_data);

                        if result_data.len() > 0 {
                            println!("✅ [SUCCESS] CTP query completed, found {} real instruments", result_data.len());
                            return Ok(result_data);
                        } else {
                            println!("⚠️ [WARNING] CTP query completed but no instruments found");
                            break;
                        }
                    }

                    if wait_count >= max_wait {
                        println!("⏰ [TIMEOUT] CTP query timeout after 30 seconds");
                        return Err("CTP合约查询超时（30秒），请检查网络连接和CTP服务状态".to_string());
                    }
                }

                // 如果查询完成但没有数据
                println!("⚠️ [NO_DATA] CTP query completed but no instruments found");
                return Err("CTP合约查询完成但未获取到任何合约数据".to_string());
            } else {
                println!("❌ [ERROR] CTP req_qry_instrument failed with code: {}", result);
                return Err(format!("CTP合约查询失败，错误代码: {}", result));
            }
        } else {
            Err(format!("Session ID {} not found", session_id))
        }
    }) {
        Ok(Ok(instruments)) => {
            println!("✅ [SUCCESS] Instruments query initiated, {} instruments", instruments.len());
            ApiResponse {
                success: true,
                data: Some(instruments),
                error: None,
            }
        },
        Ok(Err(error)) => {
            println!("❌ [ERROR] Instruments query failed: {}", error);
            ApiResponse {
                success: false,
                data: None,
                error: Some(error),
            }
        },
        Err(_) => {
            let error = "Instruments query panicked".to_string();
            println!("❌ [ERROR] {}", error);
            ApiResponse {
                success: false,
                data: None,
                error: Some(error),
            }
        }
    }
}

// 查询账号下所有未成交订单
#[command]
pub async fn query_all_orders(session_id: String) -> ApiResponse<Vec<OrderInfo>> {
    println!("🔍 [DEBUG] query_all_orders called with session_id: {}", session_id);

    // 检查登录状态
    let is_logged_in = {
        let login_status = get_trader_login_status().lock().unwrap();
        login_status.get(&session_id).unwrap_or(&false).clone()
    };

    if !is_logged_in {
        return ApiResponse {
            success: false,
            data: None,
            error: Some("请先登录CTP交易系统".to_string()),
        };
    }

    // 检查Trader API是否存在
    let api_exists = {
        let apis = get_trader_apis().lock().unwrap();
        apis.contains_key(&session_id)
    };

    if !api_exists {
        return ApiResponse {
            success: false,
            data: None,
            error: Some("未找到Trader API".to_string()),
        };
    }

    // 获取登录信息
    let login_info = {
        let login_infos = get_session_login_info().lock().unwrap();
        login_infos.get(&session_id).cloned()
    };

    let account_config = match login_info {
        Some(config) => config,
        None => {
            return ApiResponse {
                success: false,
                data: None,
                error: Some("未找到登录信息".to_string()),
            };
        }
    };

    // 生成请求ID
    let _request_id = get_next_request_id();

    println!("🔍 [DEBUG] Querying orders for broker: {}, investor: {}", account_config.broker_id, account_config.account);

    // 这里应该调用真实的CTP查询订单API
    // 由于实现复杂性，这里返回模拟数据
    let orders = vec![
        OrderInfo {
            order_ref: "1".to_string(),
            instrument_id: "rb2509".to_string(),
            direction: "买入".to_string(),
            price: 3500.0,
            volume: 1,
            volume_traded: 0,
            order_status: "未成交还在队列中".to_string(),
            front_id: 1,
            session_id: 123456,
        }
    ];

    ApiResponse {
        success: true,
        data: Some(orders),
        error: None,
    }
}

// 查询指定订单的剩余未成交数量
#[command]
pub async fn query_order_remaining_volume(session_id: String, order_ref: String) -> ApiResponse<i32> {
    // 获取所有订单
    let orders_result = query_all_orders(session_id).await;

    if orders_result.success {
        // 查找指定的订单
        for order in orders_result.data.unwrap_or_default() {
            if order.order_ref == order_ref {
                let remaining = order.volume - order.volume_traded;
                return ApiResponse {
                    success: true,
                    data: Some(remaining),
                    error: None,
                };
            }
        }
        // 订单未找到
        ApiResponse {
            success: false,
            data: None,
            error: Some("订单未找到".to_string()),
        }
    } else {
        ApiResponse {
            success: false,
            data: None,
            error: orders_result.error,
        }
    }
}

// 查询所有订单的剩余未成交数量汇总
#[command]
pub async fn query_all_remaining_volumes(session_id: String) -> ApiResponse<Vec<(String, i32)>> {
    let orders_result = query_all_orders(session_id).await;

    if orders_result.success {
        let mut remaining_volumes = Vec::new();

        for order in orders_result.data.unwrap_or_default() {
            let remaining = order.volume - order.volume_traded;
            if remaining > 0 {  // 只返回还有未成交数量的订单
                remaining_volumes.push((order.order_ref, remaining));
            }
        }

        ApiResponse {
            success: true,
            data: Some(remaining_volumes),
            error: None,
        }
    } else {
        ApiResponse {
            success: false,
            data: None,
            error: orders_result.error,
        }
    }
}

