use crate::ctp_commands::ctp::types::ApiResponse;
use std::fs;
use std::path::Path;
use tauri::command;

/// 缓存管理模块
/// 专门用于清理当前目录下的 CTP 缓存

/// 获取 CTP 缓存根目录路径（当前工作目录下的 temp/ctp_cache）
pub fn get_ctp_cache_root_path() -> Result<std::path::PathBuf, String> {
    let current_dir =
        std::env::current_dir().map_err(|e| format!("无法获取当前工作目录: {}", e))?;
    let ctp_cache_root = current_dir.join("temp").join("ctp_cache");
    Ok(ctp_cache_root)
}

/// 清理当前目录下的 CTP 缓存（专门用于清理 temp/ctp_cache）
pub fn cleanup_current_dir_cache() -> Result<String, String> {
    let cache_root = get_ctp_cache_root_path()?;
    let mut cleaned_files = 0;
    let mut skipped_files = 0;
    let mut cleaned_dirs = 0;
    let mut skipped_dirs = 0;

    if !cache_root.exists() {
        println!("ℹ️ [CACHE] 当前目录下的CTP缓存目录不存在: {:?}", cache_root);
        return Ok("缓存目录不存在，无需清理".to_string());
    }

    println!("🔄 [CACHE] 开始清理当前目录下的CTP缓存: {:?}", cache_root);

    // 递归清理缓存目录
    cleanup_directory_contents(
        &cache_root,
        &mut cleaned_files,
        &mut skipped_files,
        &mut cleaned_dirs,
        &mut skipped_dirs,
    )?;

    let result_msg = format!(
        "当前目录CTP缓存清理完成 - 文件: 已清理{}个，跳过{}个 | 目录: 已清理{}个，跳过{}个",
        cleaned_files, skipped_files, cleaned_dirs, skipped_dirs
    );
    println!("✅ [CACHE] {}", result_msg);
    Ok(result_msg)
}

/// 递归清理目录内容
fn cleanup_directory_contents(
    dir_path: &Path,
    cleaned_files: &mut i32,
    skipped_files: &mut i32,
    cleaned_dirs: &mut i32,
    skipped_dirs: &mut i32,
) -> Result<(), String> {
    match fs::read_dir(dir_path) {
        Ok(entries) => {
            for entry in entries {
                if let Ok(entry) = entry {
                    let path = entry.path();

                    if path.is_file() {
                        // 尝试删除文件
                        match fs::remove_file(&path) {
                            Ok(_) => {
                                *cleaned_files += 1;
                                println!(
                                    "🧹 [CACHE] 已删除缓存文件: {:?}",
                                    path.file_name().unwrap_or_default()
                                );
                            }
                            Err(e) => {
                                *skipped_files += 1;
                                if e.kind() == std::io::ErrorKind::PermissionDenied {
                                    println!(
                                        "⏭️ [CACHE] 跳过正在使用的文件: {:?}",
                                        path.file_name().unwrap_or_default()
                                    );
                                } else {
                                    println!(
                                        "⚠️ [CACHE] 无法删除文件 {:?}: {}",
                                        path.file_name().unwrap_or_default(),
                                        e
                                    );
                                }
                            }
                        }
                    } else if path.is_dir() {
                        // 先递归清理子目录内容
                        cleanup_directory_contents(
                            &path,
                            cleaned_files,
                            skipped_files,
                            cleaned_dirs,
                            skipped_dirs,
                        )?;

                        // 尝试删除空目录
                        match fs::remove_dir(&path) {
                            Ok(_) => {
                                *cleaned_dirs += 1;
                                println!(
                                    "🧹 [CACHE] 已删除空缓存目录: {:?}",
                                    path.file_name().unwrap_or_default()
                                );
                            }
                            Err(e) => {
                                *skipped_dirs += 1;
                                if e.kind() == std::io::ErrorKind::DirectoryNotEmpty {
                                    println!(
                                        "⏭️ [CACHE] 跳过非空目录: {:?}",
                                        path.file_name().unwrap_or_default()
                                    );
                                } else if e.kind() == std::io::ErrorKind::PermissionDenied {
                                    println!(
                                        "⏭️ [CACHE] 跳过正在使用的目录: {:?}",
                                        path.file_name().unwrap_or_default()
                                    );
                                } else {
                                    println!(
                                        "⚠️ [CACHE] 无法删除目录 {:?}: {}",
                                        path.file_name().unwrap_or_default(),
                                        e
                                    );
                                }
                            }
                        }
                    }
                }
            }
        }
        Err(e) => {
            return Err(format!("读取目录失败 {:?}: {}", dir_path, e));
        }
    }
    Ok(())
}

// Tauri 命令接口

// 清理当前目录下的 CTP 缓存
#[command]
pub fn cleanup_current_dir_cache_command() -> ApiResponse<String> {
    println!("🔄 [CACHE] 开始清理当前目录下的CTP缓存");

    match cleanup_current_dir_cache() {
        Ok(result_msg) => ApiResponse {
            success: true,
            data: Some(result_msg),
            error: None,
        },
        Err(error) => ApiResponse {
            success: false,
            data: None,
            error: Some(error),
        },
    }
}
