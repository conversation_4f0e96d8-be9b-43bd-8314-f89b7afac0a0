use crate::ctp_commands::ctp::state::{get_request_id_counter, get_order_ref_counter};

// 安全地复制字符串到CTP字符数组
pub fn copy_str_to_ctp_array(src: &str, dst: &mut [i8]) {
    let src_bytes = src.as_bytes();
    let max_len = std::cmp::min(src_bytes.len(), dst.len() - 1);

    // 清零数组
    for i in 0..dst.len() {
        dst[i] = 0;
    }

    // 复制字符串
    for i in 0..max_len {
        dst[i] = src_bytes[i] as i8;
    }
}

// 获取下一个请求ID
pub fn get_next_request_id() -> i32 {
    let mut counter = get_request_id_counter().lock().unwrap();
    let id = *counter;
    *counter += 1;
    id
}

// 获取下一个订单引用
pub fn get_next_order_ref() -> String {
    let mut counter = get_order_ref_counter().lock().unwrap();
    let ref_id = *counter;
    *counter += 1;
    format!("{}", ref_id)
}

// 获取系统临时目录的 CTP 缓存路径
pub fn get_ctp_cache_path(session_id: &str) -> Result<String, String> {
    let temp_dir = std::env::temp_dir();
    let ctp_cache_dir = temp_dir.join("ctp_cache").join(session_id);

    // 确保目录存在
    if let Err(e) = std::fs::create_dir_all(&ctp_cache_dir) {
        return Err(format!("Failed to create cache directory: {}", e));
    }

    Ok(ctp_cache_dir.to_string_lossy().to_string().replace("\\", "/"))
}

// 验证登录凭据的基本有效性
pub fn validate_login_credentials(config: &crate::ctp_commands::ctp::types::CtpAccountConfig) -> Result<(), String> {
    if config.broker_id.is_empty() {
        return Err("经纪公司代码不能为空".to_string());
    }
    if config.account.is_empty() {
        return Err("账户不能为空".to_string());
    }
    if config.password.is_empty() {
        return Err("密码不能为空".to_string());
    }
    if config.md_front.is_empty() {
        return Err("行情前置地址不能为空".to_string());
    }
    if config.trade_front.is_empty() {
        return Err("交易前置地址不能为空".to_string());
    }

    // 基本格式验证
    if !config.md_front.starts_with("tcp://") {
        return Err("行情前置地址格式错误，应以tcp://开头".to_string());
    }
    if !config.trade_front.starts_with("tcp://") {
        return Err("交易前置地址格式错误，应以tcp://开头".to_string());
    }

    Ok(())
}

// 验证订单字段的有效性
pub fn validate_order_fields(order: &crate::ctp_commands::ctp::types::OrderRequest) -> Result<(), String> {
    // 验证合约代码
    if order.instrument_id.is_empty() {
        return Err("合约代码不能为空".to_string());
    }
    if order.instrument_id.len() > 30 {
        return Err("合约代码长度不能超过30个字符".to_string());
    }

    // 验证买卖方向
    if order.direction != "0" && order.direction != "1" {
        return Err("买卖方向必须是 '0'(买入) 或 '1'(卖出)".to_string());
    }

    // 验证价格
    if order.price <= 0.0 {
        return Err("价格必须大于0".to_string());
    }
    if order.price > 999999.99 {
        return Err("价格不能超过999999.99".to_string());
    }

    // 验证数量
    if order.volume <= 0 {
        return Err("数量必须大于0".to_string());
    }
    if order.volume > 1000000 {
        return Err("数量不能超过1000000".to_string());
    }

    // 验证订单类型
    if order.order_type != "1" && order.order_type != "2" {
        return Err("订单类型必须是 '1'(市价) 或 '2'(限价)".to_string());
    }

    // 验证开平标志
    if let Some(ref offset) = order.offset_flag {
        if !["0", "1", "3", "4"].contains(&offset.as_str()) {
            return Err("开平标志必须是 '0'(开仓), '1'(平仓), '3'(平今), '4'(平昨)".to_string());
        }
    }

    // 验证投机套保标志
    if let Some(ref hedge) = order.hedge_flag {
        if !["1", "2", "3"].contains(&hedge.as_str()) {
            return Err("投机套保标志必须是 '1'(投机), '2'(套利), '3'(套保)".to_string());
        }
    }

    Ok(())
}

// 分析合约代码
pub fn analyze_instrument_id(instrument_id: &str) -> serde_json::Value {
    let mut analysis = serde_json::Map::new();

    analysis.insert("original".to_string(), serde_json::Value::String(instrument_id.to_string()));
    analysis.insert("length".to_string(), serde_json::Value::Number(serde_json::Number::from(instrument_id.len())));

    if instrument_id.len() >= 2 {
        let prefix = instrument_id.chars().take(2).collect::<String>();
        analysis.insert("prefix".to_string(), serde_json::Value::String(prefix.clone()));

        // 推断交易所
        let exchange = if instrument_id.len() >= 2 {
            let prefix_lower = prefix.to_lowercase();
            match prefix_lower.as_str() {
                "rb" | "hc" | "ss" | "wr" | "fu" | "bu" | "ru" | "sp" | "au" | "ag" |
                "cu" | "al" | "zn" | "pb" | "ni" | "sn" | "sc" | "lu" | "bc" | "nr" => "SHFE",
                _ if instrument_id.starts_with("IF") || instrument_id.starts_with("IC") ||
                     instrument_id.starts_with("IH") || instrument_id.starts_with("IM") => "CFFEX",
                "a" | "b" | "c" | "cs" | "i" | "j" | "jm" | "l" | "m" | "p" | "pp" | "v" | "y" => "DCE",
                _ => {
                    let upper_prefix = prefix.to_uppercase();
                    match upper_prefix.as_str() {
                        "CF" | "CY" | "FG" | "MA" | "OI" | "RM" | "SF" | "SM" | "SR" | "TA" | "ZC" => "CZCE",
                        _ => "UNKNOWN",
                    }
                }
            }
        } else {
            "UNKNOWN"
        };

        analysis.insert("detected_exchange".to_string(), serde_json::Value::String(exchange.to_string()));

        // 检查格式是否合理
        let format_valid = instrument_id.len() >= 4 && instrument_id.len() <= 8;
        analysis.insert("format_valid".to_string(), serde_json::Value::Bool(format_valid));
    }

    serde_json::Value::Object(analysis)
}

