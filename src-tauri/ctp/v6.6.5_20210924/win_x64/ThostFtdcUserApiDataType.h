/////////////////////////////////////////////////////////////////////////
///@system ��һ��������ϵͳ
///@company �Ϻ��ڻ���Ϣ�������޹�˾
///@file ThostFtdcUserApiDataType.h
///@brief �����˿ͻ��˽ӿ�ʹ�õ�ҵ����������
///@history
///20060106	�Ժ��		�������ļ�
/////////////////////////////////////////////////////////////////////////

#ifndef THOST_FTDCDATATYPE_H
#define THOST_FTDCDATATYPE_H

enum THOST_TE_RESUME_TYPE
{
	THOST_TERT_RESTART = 0,
	THOST_TERT_RESUME,
	THOST_TERT_QUICK,
	THOST_TERT_NONE
};

/////////////////////////////////////////////////////////////////////////
///TFtdcTraderIDType��һ������������Ա��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcTraderIDType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcInvestorIDType��һ��Ͷ���ߴ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInvestorIDType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcBrokerIDType��һ�����͹�˾��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBrokerIDType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcBrokerAbbrType��һ�����͹�˾�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBrokerAbbrType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcBrokerNameType��һ�����͹�˾��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBrokerNameType[81];

/////////////////////////////////////////////////////////////////////////
///TFtdcOldExchangeInstIDType��һ����Լ�ڽ������Ĵ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOldExchangeInstIDType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcExchangeInstIDType��һ����Լ�ڽ������Ĵ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcExchangeInstIDType[81];

/////////////////////////////////////////////////////////////////////////
///TFtdcOrderRefType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOrderRefType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcParticipantIDType��һ����Ա��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcParticipantIDType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcUserIDType��һ���û���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUserIDType[16];

/////////////////////////////////////////////////////////////////////////
///TFtdcPasswordType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPasswordType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcClientIDType��һ�����ױ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcClientIDType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcInstrumentIDType��һ����Լ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInstrumentIDType[81];

/////////////////////////////////////////////////////////////////////////
///TFtdcOldInstrumentIDType��һ����Լ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOldInstrumentIDType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcInstrumentCodeType��һ����Լ��ʶ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInstrumentCodeType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcMarketIDType��һ���г���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcMarketIDType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcProductNameType��һ����Ʒ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcProductNameType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcExchangeIDType��һ����������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcExchangeIDType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcExchangeNameType��һ����������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcExchangeNameType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcExchangeAbbrType��һ���������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcExchangeAbbrType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcExchangeFlagType��һ����������־����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcExchangeFlagType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcMacAddressType��һ��Mac��ַ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcMacAddressType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcSystemIDType��һ��ϵͳ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSystemIDType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcClientLoginRemarkType��һ���ͻ���¼��ע2����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcClientLoginRemarkType[151];

/////////////////////////////////////////////////////////////////////////
///TFtdcExchangePropertyType��һ����������������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_EXP_Normal '0'
///���ݳɽ����ɱ���
#define THOST_FTDC_EXP_GenOrderByTrade '1'

typedef char TThostFtdcExchangePropertyType;

/////////////////////////////////////////////////////////////////////////
///TFtdcDateType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcDateType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcTimeType��һ��ʱ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcTimeType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcLongTimeType��һ����ʱ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcLongTimeType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcInstrumentNameType��һ����Լ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInstrumentNameType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcSettlementGroupIDType��һ���������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSettlementGroupIDType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcOrderSysIDType��һ�������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOrderSysIDType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcTradeIDType��һ���ɽ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcTradeIDType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcCommandTypeType��һ��DB������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCommandTypeType[65];

/////////////////////////////////////////////////////////////////////////
///TFtdcOldIPAddressType��һ��IP��ַ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOldIPAddressType[16];

/////////////////////////////////////////////////////////////////////////
///TFtdcIPAddressType��һ��IP��ַ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcIPAddressType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcIPPortType��һ��IP�˿�����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcIPPortType;

/////////////////////////////////////////////////////////////////////////
///TFtdcProductInfoType��һ����Ʒ��Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcProductInfoType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcProtocolInfoType��һ��Э����Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcProtocolInfoType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcBusinessUnitType��һ��ҵ��Ԫ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBusinessUnitType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcDepositSeqNoType��һ���������ˮ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcDepositSeqNoType[15];

/////////////////////////////////////////////////////////////////////////
///TFtdcIdentifiedCardNoType��һ��֤����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcIdentifiedCardNoType[51];

/////////////////////////////////////////////////////////////////////////
///TFtdcIdCardTypeType��һ��֤����������
/////////////////////////////////////////////////////////////////////////
///��֯��������
#define THOST_FTDC_ICT_EID '0'
///�й��������֤
#define THOST_FTDC_ICT_IDCard '1'
///����֤
#define THOST_FTDC_ICT_OfficerIDCard '2'
///����֤
#define THOST_FTDC_ICT_PoliceIDCard '3'
///ʿ��֤
#define THOST_FTDC_ICT_SoldierIDCard '4'
///���ڲ�
#define THOST_FTDC_ICT_HouseholdRegister  '5'
///����
#define THOST_FTDC_ICT_Passport '6'
///̨��֤
#define THOST_FTDC_ICT_TaiwanCompatriotIDCard  '7'
///����֤
#define THOST_FTDC_ICT_HomeComingCard '8'
///Ӫҵִ�պ�
#define THOST_FTDC_ICT_LicenseNo '9'
///˰��ǼǺ�/������˰ID
#define THOST_FTDC_ICT_TaxNo 'A'
///�۰ľ��������ڵ�ͨ��֤
#define THOST_FTDC_ICT_HMMainlandTravelPermit  'B'
///̨�����������½ͨ��֤
#define THOST_FTDC_ICT_TwMainlandTravelPermit 'C'
///����
#define THOST_FTDC_ICT_DrivingLicense 'D'
///�����籣ID
#define THOST_FTDC_ICT_SocialID 'F'
///�������֤
#define THOST_FTDC_ICT_LocalID 'G'
///��ҵ�Ǽ�֤
#define THOST_FTDC_ICT_BusinessRegistration  'H'
///�۰������Ծ������֤
#define THOST_FTDC_ICT_HKMCIDCard 'I'
///���п������֤
#define THOST_FTDC_ICT_AccountsPermits 'J'
///��������þ���֤
#define THOST_FTDC_ICT_FrgPrmtRdCard 'K'
///�ʹܲ�Ʒ������
#define THOST_FTDC_ICT_CptMngPrdLetter 'L'
///�۰�̨�������֤
#define THOST_FTDC_ICT_HKMCTwResidencePermit 'M'
///ͳһ������ô���
#define THOST_FTDC_ICT_UniformSocialCreditCode 'N'
///��������֤���ļ�
#define THOST_FTDC_ICT_CorporationCertNo 'O'
///����֤��
#define THOST_FTDC_ICT_OtherCard 'x'

typedef char TThostFtdcIdCardTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOrderLocalIDType��һ�����ر����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOrderLocalIDType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcUserNameType��һ���û���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUserNameType[81];

/////////////////////////////////////////////////////////////////////////
///TFtdcPartyNameType��һ����������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPartyNameType[81];

/////////////////////////////////////////////////////////////////////////
///TFtdcErrorMsgType��һ��������Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcErrorMsgType[81];

/////////////////////////////////////////////////////////////////////////
///TFtdcFieldNameType��һ���ֶ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFieldNameType[2049];

/////////////////////////////////////////////////////////////////////////
///TFtdcFieldContentType��һ���ֶ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFieldContentType[2049];

/////////////////////////////////////////////////////////////////////////
///TFtdcSystemNameType��һ��ϵͳ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSystemNameType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcContentType��һ����Ϣ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcContentType[501];

/////////////////////////////////////////////////////////////////////////
///TFtdcInvestorRangeType��һ��Ͷ���߷�Χ����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_IR_All '1'
///Ͷ������
#define THOST_FTDC_IR_Group '2'
///��һͶ����
#define THOST_FTDC_IR_Single '3'

typedef char TThostFtdcInvestorRangeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcDepartmentRangeType��һ��Ͷ���߷�Χ����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_DR_All '1'
///��֯�ܹ�
#define THOST_FTDC_DR_Group '2'
///��һͶ����
#define THOST_FTDC_DR_Single '3'

typedef char TThostFtdcDepartmentRangeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcDataSyncStatusType��һ������ͬ��״̬����
/////////////////////////////////////////////////////////////////////////
///δͬ��
#define THOST_FTDC_DS_Asynchronous '1'
///ͬ����
#define THOST_FTDC_DS_Synchronizing '2'
///��ͬ��
#define THOST_FTDC_DS_Synchronized '3'

typedef char TThostFtdcDataSyncStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBrokerDataSyncStatusType��һ�����͹�˾����ͬ��״̬����
/////////////////////////////////////////////////////////////////////////
///��ͬ��
#define THOST_FTDC_BDS_Synchronized '1'
///ͬ����
#define THOST_FTDC_BDS_Synchronizing '2'

typedef char TThostFtdcBrokerDataSyncStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcExchangeConnectStatusType��һ������������״̬����
/////////////////////////////////////////////////////////////////////////
///û���κ�����
#define THOST_FTDC_ECS_NoConnection '1'
///�Ѿ�������Լ��ѯ����
#define THOST_FTDC_ECS_QryInstrumentSent '2'
///�Ѿ���ȡ��Ϣ
#define THOST_FTDC_ECS_GotInformation '9'

typedef char TThostFtdcExchangeConnectStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTraderConnectStatusType��һ������������Ա����״̬����
/////////////////////////////////////////////////////////////////////////
///û���κ�����
#define THOST_FTDC_TCS_NotConnected '1'
///�Ѿ�����
#define THOST_FTDC_TCS_Connected '2'
///�Ѿ�������Լ��ѯ����
#define THOST_FTDC_TCS_QryInstrumentSent '3'
///����˽����
#define THOST_FTDC_TCS_SubPrivateFlow '4'

typedef char TThostFtdcTraderConnectStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFunctionCodeType��һ�����ܴ�������
/////////////////////////////////////////////////////////////////////////
///�����첽��
#define THOST_FTDC_FC_DataAsync '1'
///ǿ���û��ǳ�
#define THOST_FTDC_FC_ForceUserLogout '2'
///��������û�����
#define THOST_FTDC_FC_UserPasswordUpdate '3'
///������͹�˾����
#define THOST_FTDC_FC_BrokerPasswordUpdate '4'
///���Ͷ���߿���
#define THOST_FTDC_FC_InvestorPasswordUpdate '5'
///��������
#define THOST_FTDC_FC_OrderInsert '6'
///��������
#define THOST_FTDC_FC_OrderAction '7'
///ͬ��ϵͳ����
#define THOST_FTDC_FC_SyncSystemData '8'
///ͬ�����͹�˾����
#define THOST_FTDC_FC_SyncBrokerData '9'
///����ͬ�����͹�˾����
#define THOST_FTDC_FC_BachSyncBrokerData 'A'
///������ѯ
#define THOST_FTDC_FC_SuperQuery 'B'
///Ԥ�񱨵�����
#define THOST_FTDC_FC_ParkedOrderInsert 'C'
///Ԥ�񱨵�����
#define THOST_FTDC_FC_ParkedOrderAction 'D'
///ͬ����̬����
#define THOST_FTDC_FC_SyncOTP 'E'
///ɾ��δ֪��
#define THOST_FTDC_FC_DeleteOrder 'F'

typedef char TThostFtdcFunctionCodeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBrokerFunctionCodeType��һ�����͹�˾���ܴ�������
/////////////////////////////////////////////////////////////////////////
///ǿ���û��ǳ�
#define THOST_FTDC_BFC_ForceUserLogout '1'
///����û�����
#define THOST_FTDC_BFC_UserPasswordUpdate '2'
///ͬ�����͹�˾����
#define THOST_FTDC_BFC_SyncBrokerData '3'
///����ͬ�����͹�˾����
#define THOST_FTDC_BFC_BachSyncBrokerData '4'
///��������
#define THOST_FTDC_BFC_OrderInsert '5'
///��������
#define THOST_FTDC_BFC_OrderAction '6'
///ȫ����ѯ
#define THOST_FTDC_BFC_AllQuery '7'
///ϵͳ���ܣ�����/�ǳ�/�޸������
#define THOST_FTDC_BFC_log 'a'
///������ѯ����ѯ�������ݣ����Լ���������ȳ���
#define THOST_FTDC_BFC_BaseQry 'b'
///���ײ�ѯ�����ɽ���ί��
#define THOST_FTDC_BFC_TradeQry 'c'
///���׹��ܣ�����������
#define THOST_FTDC_BFC_Trade 'd'
///����ת��
#define THOST_FTDC_BFC_Virement 'e'
///���ռ��
#define THOST_FTDC_BFC_Risk 'f'
///��ѯ/������ѯ�Ự�����˵�
#define THOST_FTDC_BFC_Session 'g'
///���֪ͨ����
#define THOST_FTDC_BFC_RiskNoticeCtl 'h'
///���֪ͨ����
#define THOST_FTDC_BFC_RiskNotice 'i'
///�쿴���͹�˾�ʽ�Ȩ��
#define THOST_FTDC_BFC_BrokerDeposit 'j'
///�ʽ��ѯ
#define THOST_FTDC_BFC_QueryFund 'k'
///������ѯ
#define THOST_FTDC_BFC_QueryOrder 'l'
///�ɽ���ѯ
#define THOST_FTDC_BFC_QueryTrade 'm'
///�ֲֲ�ѯ
#define THOST_FTDC_BFC_QueryPosition 'n'
///�����ѯ
#define THOST_FTDC_BFC_QueryMarketData 'o'
///�û��¼���ѯ
#define THOST_FTDC_BFC_QueryUserEvent 'p'
///����֪ͨ��ѯ
#define THOST_FTDC_BFC_QueryRiskNotify 'q'
///������ѯ
#define THOST_FTDC_BFC_QueryFundChange 'r'
///Ͷ������Ϣ��ѯ
#define THOST_FTDC_BFC_QueryInvestor 's'
///���ױ����ѯ
#define THOST_FTDC_BFC_QueryTradingCode 't'
///ǿƽ
#define THOST_FTDC_BFC_ForceClose 'u'
///ѹ������
#define THOST_FTDC_BFC_PressTest 'v'
///Ȩ�淴��
#define THOST_FTDC_BFC_RemainCalc 'w'
///���ֱֲ�֤��ָ��
#define THOST_FTDC_BFC_NetPositionInd 'x'
///����Ԥ��
#define THOST_FTDC_BFC_RiskPredict 'y'
///���ݵ���
#define THOST_FTDC_BFC_DataExport 'z'
///���ָ������
#define THOST_FTDC_BFC_RiskTargetSetup 'A'
///����Ԥ��
#define THOST_FTDC_BFC_MarketDataWarn 'B'
///ҵ��֪ͨ��ѯ
#define THOST_FTDC_BFC_QryBizNotice 'C'
///ҵ��֪ͨģ������
#define THOST_FTDC_BFC_CfgBizNotice 'D'
///ͬ����̬����
#define THOST_FTDC_BFC_SyncOTP 'E'
///����ҵ��֪ͨ
#define THOST_FTDC_BFC_SendBizNotice 'F'
///���ռ����׼����
#define THOST_FTDC_BFC_CfgRiskLevelStd 'G'
///�����ն�Ӧ������
#define THOST_FTDC_BFC_TbCommand 'H'
///ɾ��δ֪��
#define THOST_FTDC_BFC_DeleteOrder 'J'
///Ԥ�񱨵�����
#define THOST_FTDC_BFC_ParkedOrderInsert 'K'
///Ԥ�񱨵�����
#define THOST_FTDC_BFC_ParkedOrderAction 'L'
///�ʽ𲻹���������Ȩ
#define THOST_FTDC_BFC_ExecOrderNoCheck 'M'
///ָ��
#define THOST_FTDC_BFC_Designate 'N'
///֤ȯ����
#define THOST_FTDC_BFC_StockDisposal 'O'
///ϯλ�ʽ�Ԥ��
#define THOST_FTDC_BFC_BrokerDepositWarn 'Q'
///���Ҳ���Ԥ��
#define THOST_FTDC_BFC_CoverWarn 'S'
///��Ȩ����
#define THOST_FTDC_BFC_PreExecOrder 'T'
///��Ȩ���շ���
#define THOST_FTDC_BFC_ExecOrderRisk 'P'
///�ֲ��޶�Ԥ��
#define THOST_FTDC_BFC_PosiLimitWarn 'U'
///�ֲ��޶��ѯ
#define THOST_FTDC_BFC_QryPosiLimit 'V'
///����ǩ��ǩ��
#define THOST_FTDC_BFC_FBSign 'W'
///����ǩԼ��Լ
#define THOST_FTDC_BFC_FBAccount 'X'

typedef char TThostFtdcBrokerFunctionCodeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOrderActionStatusType��һ����������״̬����
/////////////////////////////////////////////////////////////////////////
///�Ѿ��ύ
#define THOST_FTDC_OAS_Submitted 'a'
///�Ѿ�����
#define THOST_FTDC_OAS_Accepted 'b'
///�Ѿ����ܾ�
#define THOST_FTDC_OAS_Rejected 'c'

typedef char TThostFtdcOrderActionStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOrderStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///ȫ���ɽ�
#define THOST_FTDC_OST_AllTraded '0'
///���ֳɽ����ڶ�����
#define THOST_FTDC_OST_PartTradedQueueing '1'
///���ֳɽ����ڶ�����
#define THOST_FTDC_OST_PartTradedNotQueueing '2'
///δ�ɽ����ڶ�����
#define THOST_FTDC_OST_NoTradeQueueing '3'
///δ�ɽ����ڶ�����
#define THOST_FTDC_OST_NoTradeNotQueueing '4'
///����
#define THOST_FTDC_OST_Canceled '5'
///δ֪
#define THOST_FTDC_OST_Unknown 'a'
///��δ����
#define THOST_FTDC_OST_NotTouched 'b'
///�Ѵ���
#define THOST_FTDC_OST_Touched 'c'

typedef char TThostFtdcOrderStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOrderSubmitStatusType��һ�������ύ״̬����
/////////////////////////////////////////////////////////////////////////
///�Ѿ��ύ
#define THOST_FTDC_OSS_InsertSubmitted '0'
///�����Ѿ��ύ
#define THOST_FTDC_OSS_CancelSubmitted '1'
///�޸��Ѿ��ύ
#define THOST_FTDC_OSS_ModifySubmitted '2'
///�Ѿ�����
#define THOST_FTDC_OSS_Accepted '3'
///�����Ѿ����ܾ�
#define THOST_FTDC_OSS_InsertRejected '4'
///�����Ѿ����ܾ�
#define THOST_FTDC_OSS_CancelRejected '5'
///�ĵ��Ѿ����ܾ�
#define THOST_FTDC_OSS_ModifyRejected '6'

typedef char TThostFtdcOrderSubmitStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPositionDateType��һ���ֲ���������
/////////////////////////////////////////////////////////////////////////
///���ճֲ�
#define THOST_FTDC_PSD_Today '1'
///��ʷ�ֲ�
#define THOST_FTDC_PSD_History '2'

typedef char TThostFtdcPositionDateType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPositionDateTypeType��һ���ֲ�������������
/////////////////////////////////////////////////////////////////////////
///ʹ����ʷ�ֲ�
#define THOST_FTDC_PDT_UseHistory '1'
///��ʹ����ʷ�ֲ�
#define THOST_FTDC_PDT_NoUseHistory '2'

typedef char TThostFtdcPositionDateTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTradingRoleType��һ�����׽�ɫ����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_ER_Broker '1'
///��Ӫ
#define THOST_FTDC_ER_Host '2'
///������
#define THOST_FTDC_ER_Maker '3'

typedef char TThostFtdcTradingRoleType;

/////////////////////////////////////////////////////////////////////////
///TFtdcProductClassType��һ����Ʒ��������
/////////////////////////////////////////////////////////////////////////
///�ڻ�
#define THOST_FTDC_PC_Futures '1'
///�ڻ���Ȩ
#define THOST_FTDC_PC_Options '2'
///���
#define THOST_FTDC_PC_Combination '3'
///����
#define THOST_FTDC_PC_Spot '4'
///��ת��
#define THOST_FTDC_PC_EFP '5'
///�ֻ���Ȩ
#define THOST_FTDC_PC_SpotOption '6'
///TAS��Լ
#define THOST_FTDC_PC_TAS '7'
///����ָ��
#define THOST_FTDC_PC_MI 'I'

typedef char TThostFtdcProductClassType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAPIProductClassType��һ����Ʒ��������
/////////////////////////////////////////////////////////////////////////
///�ڻ���һ��Լ
#define THOST_FTDC_APC_FutureSingle '1'
///��Ȩ��һ��Լ
#define THOST_FTDC_APC_OptionSingle '2'
///�ɽ����ڻ�(���ڻ���Ϻ��ڻ���һ��Լ)
#define THOST_FTDC_APC_Futures '3'
///�ɽ�����Ȩ(����Ȩ��Ϻ���Ȩ��һ��Լ)
#define THOST_FTDC_APC_Options '4'
///���µ���ϣ�Ŀǰ����DCE��ZCE���ڻ���ϣ�
#define THOST_FTDC_APC_TradingComb '5'
///���������ϣ�dce�����������Ϻ�Լ ����dce���Խ��׵ĺ�Լ��
#define THOST_FTDC_APC_UnTradingComb '6'
///���п��Խ��׺�Լ
#define THOST_FTDC_APC_AllTrading '7'
///���к�Լ���������ܽ��׺�Լ ���ã�
#define THOST_FTDC_APC_All '8'

typedef char TThostFtdcAPIProductClassType;

/////////////////////////////////////////////////////////////////////////
///TFtdcInstLifePhaseType��һ����Լ��������״̬����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_IP_NotStart '0'
///����
#define THOST_FTDC_IP_Started '1'
///ͣ��
#define THOST_FTDC_IP_Pause '2'
///����
#define THOST_FTDC_IP_Expired '3'

typedef char TThostFtdcInstLifePhaseType;

/////////////////////////////////////////////////////////////////////////
///TFtdcDirectionType��һ��������������
/////////////////////////////////////////////////////////////////////////
///��
#define THOST_FTDC_D_Buy '0'
///��
#define THOST_FTDC_D_Sell '1'

typedef char TThostFtdcDirectionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPositionTypeType��һ���ֲ���������
/////////////////////////////////////////////////////////////////////////
///���ֲ�
#define THOST_FTDC_PT_Net '1'
///�ۺϳֲ�
#define THOST_FTDC_PT_Gross '2'

typedef char TThostFtdcPositionTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPosiDirectionType��һ���ֲֶ�շ�������
/////////////////////////////////////////////////////////////////////////
///��
#define THOST_FTDC_PD_Net '1'
///��ͷ
#define THOST_FTDC_PD_Long '2'
///��ͷ
#define THOST_FTDC_PD_Short '3'

typedef char TThostFtdcPosiDirectionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSysSettlementStatusType��һ��ϵͳ����״̬����
/////////////////////////////////////////////////////////////////////////
///����Ծ
#define THOST_FTDC_SS_NonActive '1'
///����
#define THOST_FTDC_SS_Startup '2'
///����
#define THOST_FTDC_SS_Operating '3'
///����
#define THOST_FTDC_SS_Settlement '4'
///�������
#define THOST_FTDC_SS_SettlementFinished '5'

typedef char TThostFtdcSysSettlementStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRatioAttrType��һ��������������
/////////////////////////////////////////////////////////////////////////
///���׷���
#define THOST_FTDC_RA_Trade '0'
///�������
#define THOST_FTDC_RA_Settlement '1'

typedef char TThostFtdcRatioAttrType;

/////////////////////////////////////////////////////////////////////////
///TFtdcHedgeFlagType��һ��Ͷ���ױ���־����
/////////////////////////////////////////////////////////////////////////
///Ͷ��
#define THOST_FTDC_HF_Speculation '1'
///����
#define THOST_FTDC_HF_Arbitrage '2'
///�ױ�
#define THOST_FTDC_HF_Hedge '3'
///������
#define THOST_FTDC_HF_MarketMaker '5'
///��һ��Ͷ���ڶ����ױ� ������ר��
#define THOST_FTDC_HF_SpecHedge '6'
///��һ���ױ��ڶ���Ͷ��  ������ר��
#define THOST_FTDC_HF_HedgeSpec '7'

typedef char TThostFtdcHedgeFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBillHedgeFlagType��һ��Ͷ���ױ���־����
/////////////////////////////////////////////////////////////////////////
///Ͷ��
#define THOST_FTDC_BHF_Speculation '1'
///����
#define THOST_FTDC_BHF_Arbitrage '2'
///�ױ�
#define THOST_FTDC_BHF_Hedge '3'

typedef char TThostFtdcBillHedgeFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcClientIDTypeType��һ�����ױ�����������
/////////////////////////////////////////////////////////////////////////
///Ͷ��
#define THOST_FTDC_CIDT_Speculation '1'
///����
#define THOST_FTDC_CIDT_Arbitrage '2'
///�ױ�
#define THOST_FTDC_CIDT_Hedge '3'
///������
#define THOST_FTDC_CIDT_MarketMaker '5'

typedef char TThostFtdcClientIDTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOrderPriceTypeType��һ�������۸���������
/////////////////////////////////////////////////////////////////////////
///�����
#define THOST_FTDC_OPT_AnyPrice '1'
///�޼�
#define THOST_FTDC_OPT_LimitPrice '2'
///���ż�
#define THOST_FTDC_OPT_BestPrice '3'
///���¼�
#define THOST_FTDC_OPT_LastPrice '4'
///���¼۸����ϸ�1��ticks
#define THOST_FTDC_OPT_LastPricePlusOneTicks '5'
///���¼۸����ϸ�2��ticks
#define THOST_FTDC_OPT_LastPricePlusTwoTicks '6'
///���¼۸����ϸ�3��ticks
#define THOST_FTDC_OPT_LastPricePlusThreeTicks '7'
///��һ��
#define THOST_FTDC_OPT_AskPrice1 '8'
///��һ�۸����ϸ�1��ticks
#define THOST_FTDC_OPT_AskPrice1PlusOneTicks '9'
///��һ�۸����ϸ�2��ticks
#define THOST_FTDC_OPT_AskPrice1PlusTwoTicks 'A'
///��һ�۸����ϸ�3��ticks
#define THOST_FTDC_OPT_AskPrice1PlusThreeTicks 'B'
///��һ��
#define THOST_FTDC_OPT_BidPrice1 'C'
///��һ�۸����ϸ�1��ticks
#define THOST_FTDC_OPT_BidPrice1PlusOneTicks 'D'
///��һ�۸����ϸ�2��ticks
#define THOST_FTDC_OPT_BidPrice1PlusTwoTicks 'E'
///��һ�۸����ϸ�3��ticks
#define THOST_FTDC_OPT_BidPrice1PlusThreeTicks 'F'
///�嵵��
#define THOST_FTDC_OPT_FiveLevelPrice 'G'

typedef char TThostFtdcOrderPriceTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOffsetFlagType��һ����ƽ��־����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_OF_Open '0'
///ƽ��
#define THOST_FTDC_OF_Close '1'
///ǿƽ
#define THOST_FTDC_OF_ForceClose '2'
///ƽ��
#define THOST_FTDC_OF_CloseToday '3'
///ƽ��
#define THOST_FTDC_OF_CloseYesterday '4'
///ǿ��
#define THOST_FTDC_OF_ForceOff '5'
///����ǿƽ
#define THOST_FTDC_OF_LocalForceClose '6'

typedef char TThostFtdcOffsetFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcForceCloseReasonType��һ��ǿƽԭ������
/////////////////////////////////////////////////////////////////////////
///��ǿƽ
#define THOST_FTDC_FCC_NotForceClose '0'
///�ʽ���
#define THOST_FTDC_FCC_LackDeposit '1'
///�ͻ�����
#define THOST_FTDC_FCC_ClientOverPositionLimit '2'
///��Ա����
#define THOST_FTDC_FCC_MemberOverPositionLimit '3'
///�ֲַ�������
#define THOST_FTDC_FCC_NotMultiple '4'
///Υ��
#define THOST_FTDC_FCC_Violation '5'
///����
#define THOST_FTDC_FCC_Other '6'
///��Ȼ���ٽ�����
#define THOST_FTDC_FCC_PersonDeliv '7'
///���ǿƽ����֤�ʽ�
#define THOST_FTDC_FCC_Notverifycapital '8'

typedef char TThostFtdcForceCloseReasonType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOrderTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_ORDT_Normal '0'
///��������
#define THOST_FTDC_ORDT_DeriveFromQuote '1'
///�������
#define THOST_FTDC_ORDT_DeriveFromCombination '2'
///��ϱ���
#define THOST_FTDC_ORDT_Combination '3'
///������
#define THOST_FTDC_ORDT_ConditionalOrder '4'
///������
#define THOST_FTDC_ORDT_Swap '5'
///���ڽ��׳ɽ�����
#define THOST_FTDC_ORDT_DeriveFromBlockTrade '6'
///��ת�ֳɽ�����
#define THOST_FTDC_ORDT_DeriveFromEFPTrade '7'

typedef char TThostFtdcOrderTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTimeConditionType��һ����Ч����������
/////////////////////////////////////////////////////////////////////////
///������ɣ�������
#define THOST_FTDC_TC_IOC '1'
///������Ч
#define THOST_FTDC_TC_GFS '2'
///������Ч
#define THOST_FTDC_TC_GFD '3'
///ָ������ǰ��Ч
#define THOST_FTDC_TC_GTD '4'
///����ǰ��Ч
#define THOST_FTDC_TC_GTC '5'
///���Ͼ�����Ч
#define THOST_FTDC_TC_GFA '6'

typedef char TThostFtdcTimeConditionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcVolumeConditionType��һ���ɽ�����������
/////////////////////////////////////////////////////////////////////////
///�κ�����
#define THOST_FTDC_VC_AV '1'
///��С����
#define THOST_FTDC_VC_MV '2'
///ȫ������
#define THOST_FTDC_VC_CV '3'

typedef char TThostFtdcVolumeConditionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcContingentConditionType��һ��������������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_CC_Immediately '1'
///ֹ��
#define THOST_FTDC_CC_Touch '2'
///ֹӮ
#define THOST_FTDC_CC_TouchProfit '3'
///Ԥ��
#define THOST_FTDC_CC_ParkedOrder '4'
///���¼۴���������
#define THOST_FTDC_CC_LastPriceGreaterThanStopPrice '5'
///���¼۴��ڵ���������
#define THOST_FTDC_CC_LastPriceGreaterEqualStopPrice '6'
///���¼�С��������
#define THOST_FTDC_CC_LastPriceLesserThanStopPrice '7'
///���¼�С�ڵ���������
#define THOST_FTDC_CC_LastPriceLesserEqualStopPrice '8'
///��һ�۴���������
#define THOST_FTDC_CC_AskPriceGreaterThanStopPrice '9'
///��һ�۴��ڵ���������
#define THOST_FTDC_CC_AskPriceGreaterEqualStopPrice 'A'
///��һ��С��������
#define THOST_FTDC_CC_AskPriceLesserThanStopPrice 'B'
///��һ��С�ڵ���������
#define THOST_FTDC_CC_AskPriceLesserEqualStopPrice 'C'
///��һ�۴���������
#define THOST_FTDC_CC_BidPriceGreaterThanStopPrice 'D'
///��һ�۴��ڵ���������
#define THOST_FTDC_CC_BidPriceGreaterEqualStopPrice 'E'
///��һ��С��������
#define THOST_FTDC_CC_BidPriceLesserThanStopPrice 'F'
///��һ��С�ڵ���������
#define THOST_FTDC_CC_BidPriceLesserEqualStopPrice 'H'

typedef char TThostFtdcContingentConditionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcActionFlagType��һ��������־����
/////////////////////////////////////////////////////////////////////////
///ɾ��
#define THOST_FTDC_AF_Delete '0'
///�޸�
#define THOST_FTDC_AF_Modify '3'

typedef char TThostFtdcActionFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTradingRightType��һ������Ȩ������
/////////////////////////////////////////////////////////////////////////
///���Խ���
#define THOST_FTDC_TR_Allow '0'
///ֻ��ƽ��
#define THOST_FTDC_TR_CloseOnly '1'
///���ܽ���
#define THOST_FTDC_TR_Forbidden '2'

typedef char TThostFtdcTradingRightType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOrderSourceType��һ��������Դ����
/////////////////////////////////////////////////////////////////////////
///���Բ�����
#define THOST_FTDC_OSRC_Participant '0'
///���Թ���Ա
#define THOST_FTDC_OSRC_Administrator '1'

typedef char TThostFtdcOrderSourceType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTradeTypeType��һ���ɽ���������
/////////////////////////////////////////////////////////////////////////
///��ϳֲֲ��Ϊ��һ�ֲ�,��ʼ����Ӧ���������͵ĳֲ�
#define THOST_FTDC_TRDT_SplitCombination '#'
///��ͨ�ɽ�
#define THOST_FTDC_TRDT_Common '0'
///��Ȩִ��
#define THOST_FTDC_TRDT_OptionsExecution '1'
///OTC�ɽ�
#define THOST_FTDC_TRDT_OTC '2'
///��ת�������ɽ�
#define THOST_FTDC_TRDT_EFPDerived '3'
///��������ɽ�
#define THOST_FTDC_TRDT_CombinationDerived '4'
///���ڽ��׳ɽ�
#define THOST_FTDC_TRDT_BlockTrade '5'

typedef char TThostFtdcTradeTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSpecPosiTypeType��һ������ֲ���ϸ��ʶ����
/////////////////////////////////////////////////////////////////////////
///��ͨ�ֲ���ϸ
#define THOST_FTDC_SPOST_Common '#'
///TAS��Լ�ɽ������ı�ĺ�Լ�ֲ���ϸ
#define THOST_FTDC_SPOST_Tas '0'

typedef char TThostFtdcSpecPosiTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPriceSourceType��һ���ɽ�����Դ����
/////////////////////////////////////////////////////////////////////////
///ǰ�ɽ���
#define THOST_FTDC_PSRC_LastPrice '0'
///��ί�м�
#define THOST_FTDC_PSRC_Buy '1'
///��ί�м�
#define THOST_FTDC_PSRC_Sell '2'
///����ɽ���
#define THOST_FTDC_PSRC_OTC '3'

typedef char TThostFtdcPriceSourceType;

/////////////////////////////////////////////////////////////////////////
///TFtdcInstrumentStatusType��һ����Լ����״̬����
/////////////////////////////////////////////////////////////////////////
///����ǰ
#define THOST_FTDC_IS_BeforeTrading '0'
///�ǽ���
#define THOST_FTDC_IS_NoTrading '1'
///��������
#define THOST_FTDC_IS_Continous '2'
///���Ͼ��۱���
#define THOST_FTDC_IS_AuctionOrdering '3'
///���Ͼ��ۼ۸�ƽ��
#define THOST_FTDC_IS_AuctionBalance '4'
///���Ͼ��۴��
#define THOST_FTDC_IS_AuctionMatch '5'
///����
#define THOST_FTDC_IS_Closed '6'

typedef char TThostFtdcInstrumentStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcInstStatusEnterReasonType��һ��Ʒ�ֽ��뽻��״̬ԭ������
/////////////////////////////////////////////////////////////////////////
///�Զ��л�
#define THOST_FTDC_IER_Automatic '1'
///�ֶ��л�
#define THOST_FTDC_IER_Manual '2'
///�۶�
#define THOST_FTDC_IER_Fuse '3'

typedef char TThostFtdcInstStatusEnterReasonType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOrderActionRefType��һ������������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcOrderActionRefType;

/////////////////////////////////////////////////////////////////////////
///TFtdcInstallCountType��һ����װ��������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcInstallCountType;

/////////////////////////////////////////////////////////////////////////
///TFtdcInstallIDType��һ����װ�������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcInstallIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcErrorIDType��һ�������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcErrorIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSettlementIDType��һ������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcSettlementIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcVolumeType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcVolumeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFrontIDType��һ��ǰ�ñ������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcFrontIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSessionIDType��һ���Ự�������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcSessionIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSequenceNoType��һ���������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcSequenceNoType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCommandNoType��һ��DB�����������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcCommandNoType;

/////////////////////////////////////////////////////////////////////////
///TFtdcMillisecType��һ��ʱ�䣨���룩����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcMillisecType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSecType��һ��ʱ�䣨�룩����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcSecType;

/////////////////////////////////////////////////////////////////////////
///TFtdcVolumeMultipleType��һ����Լ������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcVolumeMultipleType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTradingSegmentSNType��һ�����׽׶α������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcTradingSegmentSNType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRequestIDType��һ������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcRequestIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcYearType��һ���������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcYearType;

/////////////////////////////////////////////////////////////////////////
///TFtdcMonthType��һ���·�����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcMonthType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBoolType��һ������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcBoolType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPriceType��һ���۸�����
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcPriceType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCombOffsetFlagType��һ����Ͽ�ƽ��־����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCombOffsetFlagType[5];

/////////////////////////////////////////////////////////////////////////
///TFtdcCombHedgeFlagType��һ�����Ͷ���ױ���־����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCombHedgeFlagType[5];

/////////////////////////////////////////////////////////////////////////
///TFtdcRatioType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcRatioType;

/////////////////////////////////////////////////////////////////////////
///TFtdcMoneyType��һ���ʽ�����
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcMoneyType;

/////////////////////////////////////////////////////////////////////////
///TFtdcLargeVolumeType��һ�������������
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcLargeVolumeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSequenceSeriesType��һ������ϵ�к�����
/////////////////////////////////////////////////////////////////////////
typedef short TThostFtdcSequenceSeriesType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCommPhaseNoType��һ��ͨѶʱ�α������
/////////////////////////////////////////////////////////////////////////
typedef short TThostFtdcCommPhaseNoType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSequenceLabelType��һ�����б������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSequenceLabelType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcUnderlyingMultipleType��һ��������Ʒ��������
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcUnderlyingMultipleType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPriorityType��һ�����ȼ�����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcPriorityType;

/////////////////////////////////////////////////////////////////////////
///TFtdcContractCodeType��һ����ͬ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcContractCodeType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcCityType��һ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCityType[51];

/////////////////////////////////////////////////////////////////////////
///TFtdcIsStockType��һ���Ƿ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcIsStockType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcChannelType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcChannelType[51];

/////////////////////////////////////////////////////////////////////////
///TFtdcAddressType��һ��ͨѶ��ַ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAddressType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcZipCodeType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcZipCodeType[7];

/////////////////////////////////////////////////////////////////////////
///TFtdcTelephoneType��һ����ϵ�绰����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcTelephoneType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcFaxType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFaxType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcMobileType��һ���ֻ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcMobileType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcEMailType��һ�������ʼ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcEMailType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcMemoType��һ����ע����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcMemoType[161];

/////////////////////////////////////////////////////////////////////////
///TFtdcCompanyCodeType��һ����ҵ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCompanyCodeType[51];

/////////////////////////////////////////////////////////////////////////
///TFtdcWebsiteType��һ����վ��ַ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcWebsiteType[51];

/////////////////////////////////////////////////////////////////////////
///TFtdcTaxNoType��һ��˰��ǼǺ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcTaxNoType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcBatchStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///δ�ϴ�
#define THOST_FTDC_BS_NoUpload '1'
///���ϴ�
#define THOST_FTDC_BS_Uploaded '2'
///���ʧ��
#define THOST_FTDC_BS_Failed '3'

typedef char TThostFtdcBatchStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPropertyIDType��һ�����Դ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPropertyIDType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcPropertyNameType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPropertyNameType[65];

/////////////////////////////////////////////////////////////////////////
///TFtdcLicenseNoType��һ��Ӫҵִ�պ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcLicenseNoType[51];

/////////////////////////////////////////////////////////////////////////
///TFtdcAgentIDType��һ�������˴�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAgentIDType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcAgentNameType��һ����������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAgentNameType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcAgentGroupIDType��һ�����������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAgentGroupIDType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcAgentGroupNameType��һ������������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAgentGroupNameType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcReturnStyleType��һ����Ʒ�ַ�����ʽ����
/////////////////////////////////////////////////////////////////////////
///������Ʒ��
#define THOST_FTDC_RS_All '1'
///��Ʒ��
#define THOST_FTDC_RS_ByProduct '2'

typedef char TThostFtdcReturnStyleType;

/////////////////////////////////////////////////////////////////////////
///TFtdcReturnPatternType��һ������ģʽ����
/////////////////////////////////////////////////////////////////////////
///���ɽ�����
#define THOST_FTDC_RP_ByVolume '1'
///������������
#define THOST_FTDC_RP_ByFeeOnHand '2'

typedef char TThostFtdcReturnPatternType;

/////////////////////////////////////////////////////////////////////////
///TFtdcReturnLevelType��һ��������������
/////////////////////////////////////////////////////////////////////////
///����1
#define THOST_FTDC_RL_Level1 '1'
///����2
#define THOST_FTDC_RL_Level2 '2'
///����3
#define THOST_FTDC_RL_Level3 '3'
///����4
#define THOST_FTDC_RL_Level4 '4'
///����5
#define THOST_FTDC_RL_Level5 '5'
///����6
#define THOST_FTDC_RL_Level6 '6'
///����7
#define THOST_FTDC_RL_Level7 '7'
///����8
#define THOST_FTDC_RL_Level8 '8'
///����9
#define THOST_FTDC_RL_Level9 '9'

typedef char TThostFtdcReturnLevelType;

/////////////////////////////////////////////////////////////////////////
///TFtdcReturnStandardType��һ��������׼����
/////////////////////////////////////////////////////////////////////////
///�ֽ׶η���
#define THOST_FTDC_RSD_ByPeriod '1'
///��ĳһ��׼
#define THOST_FTDC_RSD_ByStandard '2'

typedef char TThostFtdcReturnStandardType;

/////////////////////////////////////////////////////////////////////////
///TFtdcMortgageTypeType��һ����Ѻ��������
/////////////////////////////////////////////////////////////////////////
///�ʳ�
#define THOST_FTDC_MT_Out '0'
///����
#define THOST_FTDC_MT_In '1'

typedef char TThostFtdcMortgageTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcInvestorSettlementParamIDType��һ��Ͷ���߽��������������
/////////////////////////////////////////////////////////////////////////
///��Ѻ����
#define THOST_FTDC_ISPI_MortgageRatio '4'
///��֤���㷨
#define THOST_FTDC_ISPI_MarginWay '5'
///���㵥����Ƿ������Ѻ
#define THOST_FTDC_ISPI_BillDeposit '9'

typedef char TThostFtdcInvestorSettlementParamIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcExchangeSettlementParamIDType��һ�����������������������
/////////////////////////////////////////////////////////////////////////
///��Ѻ����
#define THOST_FTDC_ESPI_MortgageRatio '1'
///�����ʽ�����
#define THOST_FTDC_ESPI_OtherFundItem '2'
///�����ʽ��뽻���������
#define THOST_FTDC_ESPI_OtherFundImport '3'
///�н���������Ϳ��ý��
#define THOST_FTDC_ESPI_CFFEXMinPrepa '6'
///֣�������㷽ʽ
#define THOST_FTDC_ESPI_CZCESettlementType '7'
///������������������ȡ��ʽ
#define THOST_FTDC_ESPI_ExchDelivFeeMode '9'
///Ͷ���߽�����������ȡ��ʽ
#define THOST_FTDC_ESPI_DelivFeeMode '0'
///֣������ϳֱֲ�֤����ȡ��ʽ
#define THOST_FTDC_ESPI_CZCEComMarginType 'A'
///������������֤���Ƿ��Ż�
#define THOST_FTDC_ESPI_DceComMarginType 'B'
///��ֵ��Ȩ��֤���Żݱ���
#define THOST_FTDC_ESPI_OptOutDisCountRate 'a'
///��ͱ���ϵ��
#define THOST_FTDC_ESPI_OptMiniGuarantee 'b'

typedef char TThostFtdcExchangeSettlementParamIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSystemParamIDType��һ��ϵͳ������������
/////////////////////////////////////////////////////////////////////////
///Ͷ���ߴ�����С����
#define THOST_FTDC_SPI_InvestorIDMinLength '1'
///Ͷ�����ʺŴ�����С����
#define THOST_FTDC_SPI_AccountIDMinLength '2'
///Ͷ���߿���Ĭ�ϵ�¼Ȩ��
#define THOST_FTDC_SPI_UserRightLogon '3'
///Ͷ���߽��׽��㵥�ɽ����ܷ�ʽ
#define THOST_FTDC_SPI_SettlementBillTrade '4'
///ͳһ�������½��ױ��뷽ʽ
#define THOST_FTDC_SPI_TradingCode '5'
///�����Ƿ��жϴ���δ���˵ĳ����ͷ����ʽ�
#define THOST_FTDC_SPI_CheckFund '6'
///�Ƿ�����������ģ������Ȩ��
#define THOST_FTDC_SPI_CommModelRight '7'
///�Ƿ����ñ�֤����ģ������Ȩ��
#define THOST_FTDC_SPI_MarginModelRight '9'
///�Ƿ�淶�û����ܼ���
#define THOST_FTDC_SPI_IsStandardActive '8'
///�ϴ��Ľ����������ļ�·��
#define THOST_FTDC_SPI_UploadSettlementFile 'U'
///�ϱ���֤���������ļ�·��
#define THOST_FTDC_SPI_DownloadCSRCFile 'D'
///���ɵĽ��㵥�ļ�·��
#define THOST_FTDC_SPI_SettlementBillFile 'S'
///֤����ļ���ʶ
#define THOST_FTDC_SPI_CSRCOthersFile 'C'
///Ͷ������Ƭ·��
#define THOST_FTDC_SPI_InvestorPhoto 'P'
///ȫ�ᾭ�͹�˾�ϴ��ļ�·��
#define THOST_FTDC_SPI_CSRCData 'R'
///��������¼�뷽ʽ
#define THOST_FTDC_SPI_InvestorPwdModel 'I'
///Ͷ�����н��������ļ�����·��
#define THOST_FTDC_SPI_CFFEXInvestorSettleFile 'F'
///Ͷ���ߴ�����뷽ʽ
#define THOST_FTDC_SPI_InvestorIDType 'a'
///���߻����Ȩ��
#define THOST_FTDC_SPI_FreezeMaxReMain 'r'
///��������ز���ʵʱ�ϳ�����
#define THOST_FTDC_SPI_IsSync 'A'
///�������Ȩ������
#define THOST_FTDC_SPI_RelieveOpenLimit 'O'
///�Ƿ�淶�û���������
#define THOST_FTDC_SPI_IsStandardFreeze 'X'
///֣�����Ƿ񿪷�����Ʒ���ױ�����
#define THOST_FTDC_SPI_CZCENormalProductHedge 'B'

typedef char TThostFtdcSystemParamIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTradeParamIDType��һ������ϵͳ������������
/////////////////////////////////////////////////////////////////////////
///ϵͳ�����㷨
#define THOST_FTDC_TPID_EncryptionStandard 'E'
///ϵͳ�����㷨
#define THOST_FTDC_TPID_RiskMode 'R'
///ϵͳ�����㷨�Ƿ�ȫ�� 0-�� 1-��
#define THOST_FTDC_TPID_RiskModeGlobal 'G'
///��������㷨
#define THOST_FTDC_TPID_modeEncode 'P'
///�۸�С��λ������
#define THOST_FTDC_TPID_tickMode 'T'
///�û����Ự��
#define THOST_FTDC_TPID_SingleUserSessionMaxNum 'S'
///���������¼ʧ����
#define THOST_FTDC_TPID_LoginFailMaxNum 'L'
///�Ƿ�ǿ����֤
#define THOST_FTDC_TPID_IsAuthForce 'A'
///�Ƿ񶳽�֤ȯ�ֲ�
#define THOST_FTDC_TPID_IsPosiFreeze 'F'
///�Ƿ��޲�
#define THOST_FTDC_TPID_IsPosiLimit 'M'
///֣����ѯ��ʱ����
#define THOST_FTDC_TPID_ForQuoteTimeInterval 'Q'
///�Ƿ��ڻ��޲�
#define THOST_FTDC_TPID_IsFuturePosiLimit 'B'
///�Ƿ��ڻ��µ�Ƶ������
#define THOST_FTDC_TPID_IsFutureOrderFreq 'C'
///��Ȩ�����Ƿ����ӯ��
#define THOST_FTDC_TPID_IsExecOrderProfit 'H'
///���ڿ����Ƿ���֤�������п����Ƿ���Ԥ�������˻�
#define THOST_FTDC_TPID_IsCheckBankAcc 'I'
///����������޸�����
#define THOST_FTDC_TPID_PasswordDeadLine 'J'
///ǿ����У��
#define THOST_FTDC_TPID_IsStrongPassword 'K'
///�����ʽ���Ѻ��
#define THOST_FTDC_TPID_BalanceMorgage 'a'
///��С���볤��
#define THOST_FTDC_TPID_MinPwdLen 'O'
///IP��������½ʧ�ܴ���
#define THOST_FTDC_TPID_LoginFailMaxNumForIP 'U'
///������Ч��
#define THOST_FTDC_TPID_PasswordPeriod 'V'

typedef char TThostFtdcTradeParamIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSettlementParamValueType��һ����������ֵ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSettlementParamValueType[256];

/////////////////////////////////////////////////////////////////////////
///TFtdcCounterIDType��һ����������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCounterIDType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcInvestorGroupNameType��һ��Ͷ���߷�����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInvestorGroupNameType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcBrandCodeType��һ���ƺ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBrandCodeType[257];

/////////////////////////////////////////////////////////////////////////
///TFtdcWarehouseType��һ���ֿ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcWarehouseType[257];

/////////////////////////////////////////////////////////////////////////
///TFtdcProductDateType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcProductDateType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcGradeType��һ���ȼ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcGradeType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcClassifyType��һ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcClassifyType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcPositionType��һ����λ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPositionType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcYieldlyType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcYieldlyType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcWeightType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcWeightType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcSubEntryFundNoType��һ�������ʽ���ˮ������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcSubEntryFundNoType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFileIDType��һ���ļ���ʶ����
/////////////////////////////////////////////////////////////////////////
///�ʽ�����
#define THOST_FTDC_FI_SettlementFund 'F'
///�ɽ�����
#define THOST_FTDC_FI_Trade 'T'
///Ͷ���ֲ߳�����
#define THOST_FTDC_FI_InvestorPosition 'P'
///Ͷ���߷����ʽ�����
#define THOST_FTDC_FI_SubEntryFund 'O'
///��ϳֲ�����
#define THOST_FTDC_FI_CZCECombinationPos 'C'
///�ϱ���֤������������
#define THOST_FTDC_FI_CSRCData 'R'
///֣����ƽ���˽�����
#define THOST_FTDC_FI_CZCEClose 'L'
///֣������ƽ���˽�����
#define THOST_FTDC_FI_CZCENoClose 'N'
///�ֲ���ϸ����
#define THOST_FTDC_FI_PositionDtl 'D'
///��Ȩִ���ļ�
#define THOST_FTDC_FI_OptionStrike 'S'
///����۱ȶ��ļ�
#define THOST_FTDC_FI_SettlementPriceComparison 'M'
///�������ǳֱֲ䶯��ϸ
#define THOST_FTDC_FI_NonTradePosChange 'B'

typedef char TThostFtdcFileIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFileNameType��һ���ļ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFileNameType[257];

/////////////////////////////////////////////////////////////////////////
///TFtdcFileTypeType��һ���ļ��ϴ���������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_FUT_Settlement '0'
///�˶�
#define THOST_FTDC_FUT_Check '1'

typedef char TThostFtdcFileTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFileFormatType��һ���ļ���ʽ����
/////////////////////////////////////////////////////////////////////////
///�ı��ļ�(.txt)
#define THOST_FTDC_FFT_Txt '0'
///ѹ���ļ�(.zip)
#define THOST_FTDC_FFT_Zip '1'
///DBF�ļ�(.dbf)
#define THOST_FTDC_FFT_DBF '2'

typedef char TThostFtdcFileFormatType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFileUploadStatusType��һ���ļ�״̬����
/////////////////////////////////////////////////////////////////////////
///�ϴ��ɹ�
#define THOST_FTDC_FUS_SucceedUpload '1'
///�ϴ�ʧ��
#define THOST_FTDC_FUS_FailedUpload '2'
///����ɹ�
#define THOST_FTDC_FUS_SucceedLoad '3'
///���벿�ֳɹ�
#define THOST_FTDC_FUS_PartSucceedLoad '4'
///����ʧ��
#define THOST_FTDC_FUS_FailedLoad '5'

typedef char TThostFtdcFileUploadStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTransferDirectionType��һ���Ʋַ�������
/////////////////////////////////////////////////////////////////////////
///�Ƴ�
#define THOST_FTDC_TD_Out '0'
///����
#define THOST_FTDC_TD_In '1'

typedef char TThostFtdcTransferDirectionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcUploadModeType��һ���ϴ��ļ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUploadModeType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcAccountIDType��һ��Ͷ�����ʺ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAccountIDType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankFlagType��һ������ͳһ��ʶ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankFlagType[4];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankAccountType��һ�������˻�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankAccountType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcOpenNameType��һ�������˻��Ŀ�������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOpenNameType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcOpenBankType��һ�������˻��Ŀ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOpenBankType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankNameType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankNameType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcPublishPathType��һ������·������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPublishPathType[257];

/////////////////////////////////////////////////////////////////////////
///TFtdcOperatorIDType��һ������Ա��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOperatorIDType[65];

/////////////////////////////////////////////////////////////////////////
///TFtdcMonthCountType��һ���·���������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcMonthCountType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAdvanceMonthArrayType��һ���·���ǰ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAdvanceMonthArrayType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcDateExprType��һ�����ڱ��ʽ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcDateExprType[1025];

/////////////////////////////////////////////////////////////////////////
///TFtdcInstrumentIDExprType��һ����Լ������ʽ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInstrumentIDExprType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcInstrumentNameExprType��һ����Լ���Ʊ��ʽ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInstrumentNameExprType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcSpecialCreateRuleType��һ������Ĵ�����������
/////////////////////////////////////////////////////////////////////////
///û�����ⴴ������
#define THOST_FTDC_SC_NoSpecialRule '0'
///����������
#define THOST_FTDC_SC_NoSpringFestival '1'

typedef char TThostFtdcSpecialCreateRuleType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBasisPriceTypeType��һ�����ƻ�׼����������
/////////////////////////////////////////////////////////////////////////
///��һ��Լ�����
#define THOST_FTDC_IPT_LastSettlement '1'
///��һ��Լ���̼�
#define THOST_FTDC_IPT_LaseClose '2'

typedef char TThostFtdcBasisPriceTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcProductLifePhaseType��һ����Ʒ��������״̬����
/////////////////////////////////////////////////////////////////////////
///��Ծ
#define THOST_FTDC_PLP_Active '1'
///����Ծ
#define THOST_FTDC_PLP_NonActive '2'
///ע��
#define THOST_FTDC_PLP_Canceled '3'

typedef char TThostFtdcProductLifePhaseType;

/////////////////////////////////////////////////////////////////////////
///TFtdcDeliveryModeType��һ�����ʽ����
/////////////////////////////////////////////////////////////////////////
///�ֽ𽻸�
#define THOST_FTDC_DM_CashDeliv '1'
///ʵ�ｻ��
#define THOST_FTDC_DM_CommodityDeliv '2'

typedef char TThostFtdcDeliveryModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcLogLevelType��һ����־��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcLogLevelType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcProcessNameType��һ���洢������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcProcessNameType[257];

/////////////////////////////////////////////////////////////////////////
///TFtdcOperationMemoType��һ������ժҪ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOperationMemoType[1025];

/////////////////////////////////////////////////////////////////////////
///TFtdcFundIOTypeType��һ���������������
/////////////////////////////////////////////////////////////////////////
///�����
#define THOST_FTDC_FIOT_FundIO '1'
///����ת��
#define THOST_FTDC_FIOT_Transfer '2'
///���ڻ���
#define THOST_FTDC_FIOT_SwapCurrency '3'

typedef char TThostFtdcFundIOTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFundTypeType��һ���ʽ���������
/////////////////////////////////////////////////////////////////////////
///���д��
#define THOST_FTDC_FT_Deposite '1'
///�����ʽ�
#define THOST_FTDC_FT_ItemFund '2'
///��˾����
#define THOST_FTDC_FT_Company '3'
///�ʽ���ת
#define THOST_FTDC_FT_InnerTransfer '4'

typedef char TThostFtdcFundTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFundDirectionType��һ�������������
/////////////////////////////////////////////////////////////////////////
///���
#define THOST_FTDC_FD_In '1'
///����
#define THOST_FTDC_FD_Out '2'

typedef char TThostFtdcFundDirectionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFundStatusType��һ���ʽ�״̬����
/////////////////////////////////////////////////////////////////////////
///��¼��
#define THOST_FTDC_FS_Record '1'
///�Ѹ���
#define THOST_FTDC_FS_Check '2'
///�ѳ���
#define THOST_FTDC_FS_Charge '3'

typedef char TThostFtdcFundStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBillNoType��һ��Ʊ�ݺ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBillNoType[15];

/////////////////////////////////////////////////////////////////////////
///TFtdcBillNameType��һ��Ʊ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBillNameType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcPublishStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_PS_None '1'
///���ڷ���
#define THOST_FTDC_PS_Publishing '2'
///�ѷ���
#define THOST_FTDC_PS_Published '3'

typedef char TThostFtdcPublishStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcEnumValueIDType��һ��ö��ֵ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcEnumValueIDType[65];

/////////////////////////////////////////////////////////////////////////
///TFtdcEnumValueTypeType��һ��ö��ֵ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcEnumValueTypeType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcEnumValueLabelType��һ��ö��ֵ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcEnumValueLabelType[65];

/////////////////////////////////////////////////////////////////////////
///TFtdcEnumValueResultType��һ��ö��ֵ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcEnumValueResultType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcSystemStatusType��һ��ϵͳ״̬����
/////////////////////////////////////////////////////////////////////////
///����Ծ
#define THOST_FTDC_ES_NonActive '1'
///����
#define THOST_FTDC_ES_Startup '2'
///���׿�ʼ��ʼ��
#define THOST_FTDC_ES_Initialize '3'
///������ɳ�ʼ��
#define THOST_FTDC_ES_Initialized '4'
///���п�ʼ
#define THOST_FTDC_ES_Close '5'
///�������
#define THOST_FTDC_ES_Closed '6'
///����
#define THOST_FTDC_ES_Settlement '7'

typedef char TThostFtdcSystemStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSettlementStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///��ʼ
#define THOST_FTDC_STS_Initialize '0'
///������
#define THOST_FTDC_STS_Settlementing '1'
///�ѽ���
#define THOST_FTDC_STS_Settlemented '2'
///�������
#define THOST_FTDC_STS_Finished '3'

typedef char TThostFtdcSettlementStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRangeIntTypeType��һ���޶�ֵ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRangeIntTypeType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcRangeIntFromType��һ���޶�ֵ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRangeIntFromType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcRangeIntToType��һ���޶�ֵ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRangeIntToType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcFunctionIDType��һ�����ܴ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFunctionIDType[25];

/////////////////////////////////////////////////////////////////////////
///TFtdcFunctionValueCodeType��һ�����ܱ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFunctionValueCodeType[257];

/////////////////////////////////////////////////////////////////////////
///TFtdcFunctionNameType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFunctionNameType[65];

/////////////////////////////////////////////////////////////////////////
///TFtdcRoleIDType��һ����ɫ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRoleIDType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcRoleNameType��һ����ɫ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRoleNameType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcDescriptionType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcDescriptionType[401];

/////////////////////////////////////////////////////////////////////////
///TFtdcCombineIDType��һ����ϱ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCombineIDType[25];

/////////////////////////////////////////////////////////////////////////
///TFtdcCombineTypeType��һ�������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCombineTypeType[25];

/////////////////////////////////////////////////////////////////////////
///TFtdcInvestorTypeType��һ��Ͷ������������
/////////////////////////////////////////////////////////////////////////
///��Ȼ��
#define THOST_FTDC_CT_Person '0'
///����
#define THOST_FTDC_CT_Company '1'
///Ͷ�ʻ���
#define THOST_FTDC_CT_Fund '2'
///���ⷨ��
#define THOST_FTDC_CT_SpecialOrgan '3'
///�ʹܻ�
#define THOST_FTDC_CT_Asset '4'

typedef char TThostFtdcInvestorTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBrokerTypeType��һ�����͹�˾��������
/////////////////////////////////////////////////////////////////////////
///���׻�Ա
#define THOST_FTDC_BT_Trade '0'
///���׽����Ա
#define THOST_FTDC_BT_TradeSettle '1'

typedef char TThostFtdcBrokerTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRiskLevelType��һ�����յȼ�����
/////////////////////////////////////////////////////////////////////////
///�ͷ��տͻ�
#define THOST_FTDC_FAS_Low '1'
///��ͨ�ͻ�
#define THOST_FTDC_FAS_Normal '2'
///��ע�ͻ�
#define THOST_FTDC_FAS_Focus '3'
///���տͻ�
#define THOST_FTDC_FAS_Risk '4'

typedef char TThostFtdcRiskLevelType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFeeAcceptStyleType��һ����������ȡ��ʽ����
/////////////////////////////////////////////////////////////////////////
///��������ȡ
#define THOST_FTDC_FAS_ByTrade '1'
///��������ȡ
#define THOST_FTDC_FAS_ByDeliv '2'
///����
#define THOST_FTDC_FAS_None '3'
///��ָ����������ȡ
#define THOST_FTDC_FAS_FixFee '4'

typedef char TThostFtdcFeeAcceptStyleType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPasswordTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///��������
#define THOST_FTDC_PWDT_Trade '1'
///�ʽ�����
#define THOST_FTDC_PWDT_Account '2'

typedef char TThostFtdcPasswordTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAlgorithmType��һ��ӯ���㷨����
/////////////////////////////////////////////////////////////////////////
///��ӯ����������
#define THOST_FTDC_AG_All '1'
///��ӯ���ƣ�������
#define THOST_FTDC_AG_OnlyLost '2'
///��ӯ�ƣ���������
#define THOST_FTDC_AG_OnlyGain '3'
///��ӯ������������
#define THOST_FTDC_AG_None '4'

typedef char TThostFtdcAlgorithmType;

/////////////////////////////////////////////////////////////////////////
///TFtdcIncludeCloseProfitType��һ���Ƿ����ƽ��ӯ������
/////////////////////////////////////////////////////////////////////////
///����ƽ��ӯ��
#define THOST_FTDC_ICP_Include '0'
///������ƽ��ӯ��
#define THOST_FTDC_ICP_NotInclude '2'

typedef char TThostFtdcIncludeCloseProfitType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAllWithoutTradeType��һ���Ƿ��ܿ��������������
/////////////////////////////////////////////////////////////////////////
///�޲��޳ɽ����ܿ����������
#define THOST_FTDC_AWT_Enable '0'
///�ܿ����������
#define THOST_FTDC_AWT_Disable '2'
///�޲ֲ��ܿ����������
#define THOST_FTDC_AWT_NoHoldEnable '3'

typedef char TThostFtdcAllWithoutTradeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCommentType��һ��ӯ���㷨˵������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCommentType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcVersionType��һ���汾������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcVersionType[4];

/////////////////////////////////////////////////////////////////////////
///TFtdcTradeCodeType��һ�����״�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcTradeCodeType[7];

/////////////////////////////////////////////////////////////////////////
///TFtdcTradeDateType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcTradeDateType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcTradeTimeType��һ������ʱ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcTradeTimeType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcTradeSerialType��һ��������ˮ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcTradeSerialType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcTradeSerialNoType��һ��������ˮ������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcTradeSerialNoType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFutureIDType��һ���ڻ���˾��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFutureIDType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankIDType��һ�����д�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankIDType[4];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankBrchIDType��һ�����з����Ĵ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankBrchIDType[5];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankBranchIDType��һ�������Ĵ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankBranchIDType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcOperNoType��һ�����׹�Ա����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOperNoType[17];

/////////////////////////////////////////////////////////////////////////
///TFtdcDeviceIDType��һ��������־����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcDeviceIDType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcRecordNumType��һ����¼������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRecordNumType[7];

/////////////////////////////////////////////////////////////////////////
///TFtdcFutureAccountType��һ���ڻ��ʽ��˺�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFutureAccountType[22];

/////////////////////////////////////////////////////////////////////////
///TFtdcFuturePwdFlagType��һ���ʽ�����˶Ա�־����
/////////////////////////////////////////////////////////////////////////
///���˶�
#define THOST_FTDC_FPWD_UnCheck '0'
///�˶�
#define THOST_FTDC_FPWD_Check '1'

typedef char TThostFtdcFuturePwdFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTransferTypeType��һ������ת����������
/////////////////////////////////////////////////////////////////////////
///����ת�ڻ�
#define THOST_FTDC_TT_BankToFuture '0'
///�ڻ�ת����
#define THOST_FTDC_TT_FutureToBank '1'

typedef char TThostFtdcTransferTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFutureAccPwdType��һ���ڻ��ʽ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFutureAccPwdType[17];

/////////////////////////////////////////////////////////////////////////
///TFtdcCurrencyCodeType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCurrencyCodeType[4];

/////////////////////////////////////////////////////////////////////////
///TFtdcRetCodeType��һ����Ӧ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRetCodeType[5];

/////////////////////////////////////////////////////////////////////////
///TFtdcRetInfoType��һ����Ӧ��Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRetInfoType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcTradeAmtType��һ���������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcTradeAmtType[20];

/////////////////////////////////////////////////////////////////////////
///TFtdcUseAmtType��һ�����п����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUseAmtType[20];

/////////////////////////////////////////////////////////////////////////
///TFtdcFetchAmtType��һ�����п�ȡ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFetchAmtType[20];

/////////////////////////////////////////////////////////////////////////
///TFtdcTransferValidFlagType��һ��ת����Ч��־����
/////////////////////////////////////////////////////////////////////////
///��Ч��ʧ��
#define THOST_FTDC_TVF_Invalid '0'
///��Ч
#define THOST_FTDC_TVF_Valid '1'
///����
#define THOST_FTDC_TVF_Reverse '2'

typedef char TThostFtdcTransferValidFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCertCodeType��һ��֤����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCertCodeType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcReasonType��һ����������
/////////////////////////////////////////////////////////////////////////
///��
#define THOST_FTDC_RN_CD '0'
///�ʽ���;
#define THOST_FTDC_RN_ZT '1'
///����
#define THOST_FTDC_RN_QT '2'

typedef char TThostFtdcReasonType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFundProjectIDType��һ���ʽ���Ŀ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFundProjectIDType[5];

/////////////////////////////////////////////////////////////////////////
///TFtdcSexType��һ���Ա�����
/////////////////////////////////////////////////////////////////////////
///δ֪
#define THOST_FTDC_SEX_None '0'
///��
#define THOST_FTDC_SEX_Man '1'
///Ů
#define THOST_FTDC_SEX_Woman '2'

typedef char TThostFtdcSexType;

/////////////////////////////////////////////////////////////////////////
///TFtdcProfessionType��һ��ְҵ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcProfessionType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcNationalType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcNationalType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcProvinceType��һ��ʡ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcProvinceType[51];

/////////////////////////////////////////////////////////////////////////
///TFtdcRegionType��һ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRegionType[16];

/////////////////////////////////////////////////////////////////////////
///TFtdcCountryType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCountryType[16];

/////////////////////////////////////////////////////////////////////////
///TFtdcLicenseNOType��һ��Ӫҵִ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcLicenseNOType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcCompanyTypeType��һ����ҵ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCompanyTypeType[16];

/////////////////////////////////////////////////////////////////////////
///TFtdcBusinessScopeType��һ����Ӫ��Χ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBusinessScopeType[1001];

/////////////////////////////////////////////////////////////////////////
///TFtdcCapitalCurrencyType��һ��ע���ʱ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCapitalCurrencyType[4];

/////////////////////////////////////////////////////////////////////////
///TFtdcUserTypeType��һ���û���������
/////////////////////////////////////////////////////////////////////////
///Ͷ����
#define THOST_FTDC_UT_Investor '0'
///����Ա
#define THOST_FTDC_UT_Operator '1'
///����Ա
#define THOST_FTDC_UT_SuperUser '2'

typedef char TThostFtdcUserTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBranchIDType��һ��Ӫҵ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBranchIDType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcRateTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///��֤����
#define THOST_FTDC_RATETYPE_MarginRate '2'

typedef char TThostFtdcRateTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcNoteTypeType��һ��֪ͨ��������
/////////////////////////////////////////////////////////////////////////
///���׽��㵥
#define THOST_FTDC_NOTETYPE_TradeSettleBill '1'
///���׽����±�
#define THOST_FTDC_NOTETYPE_TradeSettleMonth '2'
///׷�ӱ�֤��֪ͨ��
#define THOST_FTDC_NOTETYPE_CallMarginNotes '3'
///ǿ��ƽ��֪ͨ��
#define THOST_FTDC_NOTETYPE_ForceCloseNotes '4'
///�ɽ�֪ͨ��
#define THOST_FTDC_NOTETYPE_TradeNotes '5'
///����֪ͨ��
#define THOST_FTDC_NOTETYPE_DelivNotes '6'

typedef char TThostFtdcNoteTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSettlementStyleType��һ�����㵥��ʽ����
/////////////////////////////////////////////////////////////////////////
///���ն���
#define THOST_FTDC_SBS_Day '1'
///��ʶԳ�
#define THOST_FTDC_SBS_Volume '2'

typedef char TThostFtdcSettlementStyleType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBrokerDNSType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBrokerDNSType[256];

/////////////////////////////////////////////////////////////////////////
///TFtdcSentenceType��һ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSentenceType[501];

/////////////////////////////////////////////////////////////////////////
///TFtdcSettlementBillTypeType��һ�����㵥��������
/////////////////////////////////////////////////////////////////////////
///�ձ�
#define THOST_FTDC_ST_Day '0'
///�±�
#define THOST_FTDC_ST_Month '1'

typedef char TThostFtdcSettlementBillTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcUserRightTypeType��һ���ͻ�Ȩ����������
/////////////////////////////////////////////////////////////////////////
///��¼
#define THOST_FTDC_URT_Logon '1'
///����ת��
#define THOST_FTDC_URT_Transfer '2'
///�ʼĽ��㵥
#define THOST_FTDC_URT_EMail '3'
///������㵥
#define THOST_FTDC_URT_Fax '4'
///������
#define THOST_FTDC_URT_ConditionOrder '5'

typedef char TThostFtdcUserRightTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcMarginPriceTypeType��һ����֤��۸���������
/////////////////////////////////////////////////////////////////////////
///������
#define THOST_FTDC_MPT_PreSettlementPrice '1'
///���¼�
#define THOST_FTDC_MPT_SettlementPrice '2'
///�ɽ�����
#define THOST_FTDC_MPT_AveragePrice '3'
///���ּ�
#define THOST_FTDC_MPT_OpenPrice '4'

typedef char TThostFtdcMarginPriceTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBillGenStatusType��һ�����㵥����״̬����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_BGS_None '0'
///������
#define THOST_FTDC_BGS_NoGenerated '1'
///������
#define THOST_FTDC_BGS_Generated '2'

typedef char TThostFtdcBillGenStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAlgoTypeType��һ���㷨��������
/////////////////////////////////////////////////////////////////////////
///�ֲִ����㷨
#define THOST_FTDC_AT_HandlePositionAlgo '1'
///Ѱ�ұ�֤�����㷨
#define THOST_FTDC_AT_FindMarginRateAlgo '2'

typedef char TThostFtdcAlgoTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcHandlePositionAlgoIDType��һ���ֲִ����㷨�������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_HPA_Base '1'
///������Ʒ������
#define THOST_FTDC_HPA_DCE '2'
///֣����Ʒ������
#define THOST_FTDC_HPA_CZCE '3'

typedef char TThostFtdcHandlePositionAlgoIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFindMarginRateAlgoIDType��һ��Ѱ�ұ�֤�����㷨�������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_FMRA_Base '1'
///������Ʒ������
#define THOST_FTDC_FMRA_DCE '2'
///֣����Ʒ������
#define THOST_FTDC_FMRA_CZCE '3'

typedef char TThostFtdcFindMarginRateAlgoIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcHandleTradingAccountAlgoIDType��һ���ʽ����㷨�������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_HTAA_Base '1'
///������Ʒ������
#define THOST_FTDC_HTAA_DCE '2'
///֣����Ʒ������
#define THOST_FTDC_HTAA_CZCE '3'

typedef char TThostFtdcHandleTradingAccountAlgoIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPersonTypeType��һ����ϵ����������
/////////////////////////////////////////////////////////////////////////
///ָ���µ���
#define THOST_FTDC_PST_Order '1'
///������Ȩ��
#define THOST_FTDC_PST_Open '2'
///�ʽ������
#define THOST_FTDC_PST_Fund '3'
///���㵥ȷ����
#define THOST_FTDC_PST_Settlement '4'
///����
#define THOST_FTDC_PST_Company '5'
///���˴���
#define THOST_FTDC_PST_Corporation '6'
///Ͷ������ϵ��
#define THOST_FTDC_PST_LinkMan '7'
///�ֻ������ʲ�������
#define THOST_FTDC_PST_Ledger '8'
///�У���������
#define THOST_FTDC_PST_Trustee '9'
///�У������ܻ������˴���
#define THOST_FTDC_PST_TrusteeCorporation 'A'
///�У������ܻ���������Ȩ��
#define THOST_FTDC_PST_TrusteeOpen 'B'
///�У������ܻ�����ϵ��
#define THOST_FTDC_PST_TrusteeContact 'C'
///������Ȼ�˲ο�֤��
#define THOST_FTDC_PST_ForeignerRefer 'D'
///���˴���ο�֤��
#define THOST_FTDC_PST_CorporationRefer 'E'

typedef char TThostFtdcPersonTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcQueryInvestorRangeType��һ����ѯ��Χ����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_QIR_All '1'
///��ѯ����
#define THOST_FTDC_QIR_Group '2'
///��һͶ����
#define THOST_FTDC_QIR_Single '3'

typedef char TThostFtdcQueryInvestorRangeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcInvestorRiskStatusType��һ��Ͷ���߷���״̬����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_IRS_Normal '1'
///����
#define THOST_FTDC_IRS_Warn '2'
///׷��
#define THOST_FTDC_IRS_Call '3'
///ǿƽ
#define THOST_FTDC_IRS_Force '4'
///�쳣
#define THOST_FTDC_IRS_Exception '5'

typedef char TThostFtdcInvestorRiskStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcLegIDType��һ�����ȱ������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcLegIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcLegMultipleType��һ�����ȳ�������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcLegMultipleType;

/////////////////////////////////////////////////////////////////////////
///TFtdcImplyLevelType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcImplyLevelType;

/////////////////////////////////////////////////////////////////////////
///TFtdcClearAccountType��һ�������˻�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcClearAccountType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcOrganNOType��һ�������˻�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOrganNOType[6];

/////////////////////////////////////////////////////////////////////////
///TFtdcClearbarchIDType��һ�������˻����к�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcClearbarchIDType[6];

/////////////////////////////////////////////////////////////////////////
///TFtdcUserEventTypeType��һ���û��¼���������
/////////////////////////////////////////////////////////////////////////
///��¼
#define THOST_FTDC_UET_Login '1'
///�ǳ�
#define THOST_FTDC_UET_Logout '2'
///���׳ɹ�
#define THOST_FTDC_UET_Trading '3'
///����ʧ��
#define THOST_FTDC_UET_TradingError '4'
///�޸�����
#define THOST_FTDC_UET_UpdatePassword '5'
///�ͻ�����֤
#define THOST_FTDC_UET_Authenticate '6'
///�ն���Ϣ�ϱ�
#define THOST_FTDC_UET_SubmitSysInfo '7'
///ת��
#define THOST_FTDC_UET_Transfer '8'
///����
#define THOST_FTDC_UET_Other '9'

typedef char TThostFtdcUserEventTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcUserEventInfoType��һ���û��¼���Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUserEventInfoType[1025];

/////////////////////////////////////////////////////////////////////////
///TFtdcCloseStyleType��һ��ƽ�ַ�ʽ����
/////////////////////////////////////////////////////////////////////////
///�ȿ���ƽ
#define THOST_FTDC_ICS_Close '0'
///��ƽ����ƽ��
#define THOST_FTDC_ICS_CloseToday '1'

typedef char TThostFtdcCloseStyleType;

/////////////////////////////////////////////////////////////////////////
///TFtdcStatModeType��һ��ͳ�Ʒ�ʽ����
/////////////////////////////////////////////////////////////////////////
///----
#define THOST_FTDC_SM_Non '0'
///����Լͳ��
#define THOST_FTDC_SM_Instrument '1'
///����Ʒͳ��
#define THOST_FTDC_SM_Product '2'
///��Ͷ����ͳ��
#define THOST_FTDC_SM_Investor '3'

typedef char TThostFtdcStatModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcParkedOrderStatusType��һ��Ԥ��״̬����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_PAOS_NotSend '1'
///�ѷ���
#define THOST_FTDC_PAOS_Send '2'
///��ɾ��
#define THOST_FTDC_PAOS_Deleted '3'

typedef char TThostFtdcParkedOrderStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcParkedOrderIDType��һ��Ԥ�񱨵��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcParkedOrderIDType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcParkedOrderActionIDType��һ��Ԥ�񳷵��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcParkedOrderActionIDType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcVirDealStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///���ڴ���
#define THOST_FTDC_VDS_Dealing '1'
///����ɹ�
#define THOST_FTDC_VDS_DeaclSucceed '2'

typedef char TThostFtdcVirDealStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOrgSystemIDType��һ��ԭ��ϵͳ��������
/////////////////////////////////////////////////////////////////////////
///�ۺϽ���ƽ̨
#define THOST_FTDC_ORGS_Standard '0'
///��ʢϵͳ
#define THOST_FTDC_ORGS_ESunny '1'
///���˴�V6ϵͳ
#define THOST_FTDC_ORGS_KingStarV6 '2'

typedef char TThostFtdcOrgSystemIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcVirTradeStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///����������
#define THOST_FTDC_VTS_NaturalDeal '0'
///�ɹ�����
#define THOST_FTDC_VTS_SucceedEnd '1'
///ʧ�ܽ���
#define THOST_FTDC_VTS_FailedEND '2'
///�쳣��
#define THOST_FTDC_VTS_Exception '3'
///���˹��쳣����
#define THOST_FTDC_VTS_ManualDeal '4'
///ͨѶ�쳣 �����˹�����
#define THOST_FTDC_VTS_MesException '5'
///ϵͳ�������˹�����
#define THOST_FTDC_VTS_SysException '6'

typedef char TThostFtdcVirTradeStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcVirBankAccTypeType��һ�������ʻ���������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_VBAT_BankBook '1'
///���
#define THOST_FTDC_VBAT_BankCard '2'
///���ÿ�
#define THOST_FTDC_VBAT_CreditCard '3'

typedef char TThostFtdcVirBankAccTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcVirementStatusType��һ�������ʻ���������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_VMS_Natural '0'
///����
#define THOST_FTDC_VMS_Canceled '9'

typedef char TThostFtdcVirementStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcVirementAvailAbilityType��һ����Ч��־����
/////////////////////////////////////////////////////////////////////////
///δȷ��
#define THOST_FTDC_VAA_NoAvailAbility '0'
///��Ч
#define THOST_FTDC_VAA_AvailAbility '1'
///����
#define THOST_FTDC_VAA_Repeal '2'

typedef char TThostFtdcVirementAvailAbilityType;

/////////////////////////////////////////////////////////////////////////
///TFtdcVirementTradeCodeType��һ�����״�������
/////////////////////////////////////////////////////////////////////////
///���з��������ʽ�ת�ڻ�
#define THOST_FTDC_VTC_BankBankToFuture '102001'
///���з����ڻ��ʽ�ת����
#define THOST_FTDC_VTC_BankFutureToBank '102002'
///�ڻ����������ʽ�ת�ڻ�
#define THOST_FTDC_VTC_FutureBankToFuture '202001'
///�ڻ������ڻ��ʽ�ת����
#define THOST_FTDC_VTC_FutureFutureToBank '202002'

typedef char TThostFtdcVirementTradeCodeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPhotoTypeNameType��һ��Ӱ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPhotoTypeNameType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcPhotoTypeIDType��һ��Ӱ�����ʹ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPhotoTypeIDType[5];

/////////////////////////////////////////////////////////////////////////
///TFtdcPhotoNameType��һ��Ӱ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPhotoNameType[161];

/////////////////////////////////////////////////////////////////////////
///TFtdcTopicIDType��һ�������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcTopicIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcReportTypeIDType��һ�����ױ������ͱ�ʶ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcReportTypeIDType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcCharacterIDType��һ������������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCharacterIDType[5];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLParamIDType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLParamIDType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLInvestorTypeType��һ��Ͷ������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLInvestorTypeType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLIdCardTypeType��һ��֤����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLIdCardTypeType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLTradeDirectType��һ���ʽ������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLTradeDirectType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLTradeModelType��һ���ʽ������ʽ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLTradeModelType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLOpParamValueType��һ��ҵ���������ֵ����
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcAMLOpParamValueType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLCustomerCardTypeType��һ���ͻ����֤��/֤���ļ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLCustomerCardTypeType[81];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLInstitutionNameType��һ�����ڻ���������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLInstitutionNameType[65];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLDistrictIDType��һ�����ڻ����������ڵ�������������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLDistrictIDType[7];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLRelationShipType��һ�����ڻ�����������׵Ĺ�ϵ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLRelationShipType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLInstitutionTypeType��һ�����ڻ������������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLInstitutionTypeType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLInstitutionIDType��һ�����ڻ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLInstitutionIDType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLAccountTypeType��һ���˻���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLAccountTypeType[5];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLTradingTypeType��һ�����׷�ʽ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLTradingTypeType[7];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLTransactClassType��һ��������֧���׷������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLTransactClassType[7];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLCapitalIOType��һ���ʽ��ո���ʶ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLCapitalIOType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLSiteType��һ�����׵ص�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLSiteType[10];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLCapitalPurposeType��һ���ʽ���;����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLCapitalPurposeType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLReportTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLReportTypeType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLSerialNoType��һ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLSerialNoType[5];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLStatusType��һ��״̬����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLStatusType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLGenStatusType��һ��Aml���ɷ�ʽ����
/////////////////////////////////////////////////////////////////////////
///��������
#define THOST_FTDC_GEN_Program '0'
///�˹�����
#define THOST_FTDC_GEN_HandWork '1'

typedef char TThostFtdcAMLGenStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLSeqCodeType��һ��ҵ���ʶ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLSeqCodeType[65];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLFileNameType��һ��AML�ļ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLFileNameType[257];

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLMoneyType��һ����ϴǮ�ʽ�����
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcAMLMoneyType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLFileAmountType��һ����ϴǮ�ʽ�����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcAMLFileAmountType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCFMMCKeyType��һ����Կ����(��֤����)����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCFMMCKeyType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcCFMMCTokenType��һ����������(��֤����)����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCFMMCTokenType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcCFMMCKeyKindType��һ����̬��Կ���(��֤����)����
/////////////////////////////////////////////////////////////////////////
///�����������
#define THOST_FTDC_CFMMCKK_REQUEST 'R'
///CFMMC�Զ�����
#define THOST_FTDC_CFMMCKK_AUTO 'A'
///CFMMC�ֶ�����
#define THOST_FTDC_CFMMCKK_MANUAL 'M'

typedef char TThostFtdcCFMMCKeyKindType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLReportNameType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAMLReportNameType[81];

/////////////////////////////////////////////////////////////////////////
///TFtdcIndividualNameType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcIndividualNameType[51];

/////////////////////////////////////////////////////////////////////////
///TFtdcCurrencyIDType��һ�����ִ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCurrencyIDType[4];

/////////////////////////////////////////////////////////////////////////
///TFtdcCustNumberType��һ���ͻ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCustNumberType[36];

/////////////////////////////////////////////////////////////////////////
///TFtdcOrganCodeType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOrganCodeType[36];

/////////////////////////////////////////////////////////////////////////
///TFtdcOrganNameType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOrganNameType[71];

/////////////////////////////////////////////////////////////////////////
///TFtdcSuperOrganCodeType��һ���ϼ���������,���ڻ���˾�ܲ���������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSuperOrganCodeType[12];

/////////////////////////////////////////////////////////////////////////
///TFtdcSubBranchIDType��һ����֧��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSubBranchIDType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcSubBranchNameType��һ����֧������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSubBranchNameType[71];

/////////////////////////////////////////////////////////////////////////
///TFtdcBranchNetCodeType��һ���������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBranchNetCodeType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcBranchNetNameType��һ������������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBranchNetNameType[71];

/////////////////////////////////////////////////////////////////////////
///TFtdcOrganFlagType��һ��������ʶ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOrganFlagType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankCodingForFutureType��һ�����ж��ڻ���˾�ı�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankCodingForFutureType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankReturnCodeType��һ�����жԷ�����Ķ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankReturnCodeType[7];

/////////////////////////////////////////////////////////////////////////
///TFtdcPlateReturnCodeType��һ������ת��ƽ̨�Է�����Ķ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPlateReturnCodeType[5];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankSubBranchIDType��һ�����з�֧������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankSubBranchIDType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcFutureBranchIDType��һ���ڻ���֧������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFutureBranchIDType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcReturnCodeType��һ�����ش�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcReturnCodeType[7];

/////////////////////////////////////////////////////////////////////////
///TFtdcOperatorCodeType��һ������Ա����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOperatorCodeType[17];

/////////////////////////////////////////////////////////////////////////
///TFtdcClearDepIDType��һ�����������ʻ�����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcClearDepIDType[6];

/////////////////////////////////////////////////////////////////////////
///TFtdcClearBrchIDType��һ�����������ʻ����к�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcClearBrchIDType[6];

/////////////////////////////////////////////////////////////////////////
///TFtdcClearNameType��һ�����������ʻ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcClearNameType[71];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankAccountNameType��һ�������ʻ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankAccountNameType[71];

/////////////////////////////////////////////////////////////////////////
///TFtdcInvDepIDType��һ������Ͷ�����˺Ż���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInvDepIDType[6];

/////////////////////////////////////////////////////////////////////////
///TFtdcInvBrchIDType��һ������Ͷ�������к�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInvBrchIDType[6];

/////////////////////////////////////////////////////////////////////////
///TFtdcMessageFormatVersionType��һ����Ϣ��ʽ�汾����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcMessageFormatVersionType[36];

/////////////////////////////////////////////////////////////////////////
///TFtdcDigestType��һ��ժҪ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcDigestType[36];

/////////////////////////////////////////////////////////////////////////
///TFtdcAuthenticDataType��һ����֤��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAuthenticDataType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcPasswordKeyType��һ����Կ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPasswordKeyType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcFutureAccountNameType��һ���ڻ��ʻ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFutureAccountNameType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcMobilePhoneType��һ���ֻ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcMobilePhoneType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcFutureMainKeyType��һ���ڻ���˾����Կ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFutureMainKeyType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcFutureWorkKeyType��һ���ڻ���˾������Կ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFutureWorkKeyType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcFutureTransKeyType��һ���ڻ���˾������Կ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFutureTransKeyType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankMainKeyType��һ����������Կ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankMainKeyType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankWorkKeyType��һ�����й�����Կ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankWorkKeyType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankTransKeyType��һ�����д�����Կ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankTransKeyType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankServerDescriptionType��һ�����з�����������Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankServerDescriptionType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcAddInfoType��һ��������Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAddInfoType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcDescrInfoForReturnCodeType��һ����������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcDescrInfoForReturnCodeType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcCountryCodeType��һ�����Ҵ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCountryCodeType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcSerialType��һ����ˮ������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcSerialType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPlateSerialType��һ��ƽ̨��ˮ������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcPlateSerialType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBankSerialType��һ��������ˮ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankSerialType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcCorrectSerialType��һ��������������ˮ������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcCorrectSerialType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFutureSerialType��һ���ڻ���˾��ˮ������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcFutureSerialType;

/////////////////////////////////////////////////////////////////////////
///TFtdcApplicationIDType��һ��Ӧ�ñ�ʶ����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcApplicationIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBankProxyIDType��һ�����д����ʶ����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcBankProxyIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBTCoreIDType��һ������ת�ʺ���ϵͳ��ʶ����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcFBTCoreIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcServerPortType��һ������˿ں�����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcServerPortType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRepealedTimesType��һ���Ѿ�������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcRepealedTimesType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRepealTimeIntervalType��һ������ʱ��������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcRepealTimeIntervalType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTotalTimesType��һ��ÿ���ۼ�ת�ʴ�������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcTotalTimesType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBTRequestIDType��һ������ID����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcFBTRequestIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTIDType��һ������ID����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcTIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTradeAmountType��һ�����׽�Ԫ������
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcTradeAmountType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCustFeeType��һ��Ӧ�տͻ����ã�Ԫ������
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcCustFeeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFutureFeeType��һ��Ӧ���ڻ���˾���ã�Ԫ������
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcFutureFeeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSingleMaxAmtType��һ����������޶�����
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcSingleMaxAmtType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSingleMinAmtType��һ����������޶�����
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcSingleMinAmtType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTotalAmtType��һ��ÿ���ۼ�ת�ʶ������
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcTotalAmtType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCertificationTypeType��һ��֤����������
/////////////////////////////////////////////////////////////////////////
///���֤
#define THOST_FTDC_CFT_IDCard '0'
///����
#define THOST_FTDC_CFT_Passport '1'
///����֤
#define THOST_FTDC_CFT_OfficerIDCard '2'
///ʿ��֤
#define THOST_FTDC_CFT_SoldierIDCard '3'
///����֤
#define THOST_FTDC_CFT_HomeComingCard '4'
///���ڲ�
#define THOST_FTDC_CFT_HouseholdRegister  '5'
///Ӫҵִ�պ�
#define THOST_FTDC_CFT_LicenseNo '6'
///��֯��������֤
#define THOST_FTDC_CFT_InstitutionCodeCard '7'
///��ʱӪҵִ�պ�
#define THOST_FTDC_CFT_TempLicenseNo '8'
///������ҵ�Ǽ�֤��
#define THOST_FTDC_CFT_NoEnterpriseLicenseNo '9'
///����֤��
#define THOST_FTDC_CFT_OtherCard 'x'
///���ܲ�������
#define THOST_FTDC_CFT_SuperDepAgree 'a'

typedef char TThostFtdcCertificationTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFileBusinessCodeType��һ���ļ�ҵ��������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_FBC_Others '0'
///ת�˽�����ϸ����
#define THOST_FTDC_FBC_TransferDetails '1'
///�ͻ��˻�״̬����
#define THOST_FTDC_FBC_CustAccStatus '2'
///�˻��ཻ����ϸ����
#define THOST_FTDC_FBC_AccountTradeDetails '3'
///�ڻ��˻���Ϣ�����ϸ����
#define THOST_FTDC_FBC_FutureAccountChangeInfoDetails '4'
///�ͻ��ʽ�̨�������ϸ����
#define THOST_FTDC_FBC_CustMoneyDetail '5'
///�ͻ�������Ϣ��ϸ����
#define THOST_FTDC_FBC_CustCancelAccountInfo '6'
///�ͻ��ʽ������˽��
#define THOST_FTDC_FBC_CustMoneyResult '7'
///���������쳣����ļ�
#define THOST_FTDC_FBC_OthersExceptionResult '8'
///�ͻ���Ϣ������ϸ
#define THOST_FTDC_FBC_CustInterestNetMoneyDetails '9'
///�ͻ��ʽ�����ϸ
#define THOST_FTDC_FBC_CustMoneySendAndReceiveDetails 'a'
///���˴�������ʽ��ջ���
#define THOST_FTDC_FBC_CorporationMoneyTotal 'b'
///������ʽ��ջ���
#define THOST_FTDC_FBC_MainbodyMoneyTotal 'c'
///�ܷ�ƽ��������
#define THOST_FTDC_FBC_MainPartMonitorData 'd'
///������б��������
#define THOST_FTDC_FBC_PreparationMoney 'e'
///Э���������ʽ�������
#define THOST_FTDC_FBC_BankMoneyMonitorData 'f'

typedef char TThostFtdcFileBusinessCodeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCashExchangeCodeType��һ���㳮��־����
/////////////////////////////////////////////////////////////////////////
///��
#define THOST_FTDC_CEC_Exchange '1'
///��
#define THOST_FTDC_CEC_Cash '2'

typedef char TThostFtdcCashExchangeCodeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcYesNoIndicatorType��һ���ǻ���ʶ����
/////////////////////////////////////////////////////////////////////////
///��
#define THOST_FTDC_YNI_Yes '0'
///��
#define THOST_FTDC_YNI_No '1'

typedef char TThostFtdcYesNoIndicatorType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBanlanceTypeType��һ�������������
/////////////////////////////////////////////////////////////////////////
///��ǰ���
#define THOST_FTDC_BLT_CurrentMoney '0'
///�������
#define THOST_FTDC_BLT_UsableMoney '1'
///��ȡ���
#define THOST_FTDC_BLT_FetchableMoney '2'
///�������
#define THOST_FTDC_BLT_FreezeMoney '3'

typedef char TThostFtdcBanlanceTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcGenderType��һ���Ա�����
/////////////////////////////////////////////////////////////////////////
///δ֪״̬
#define THOST_FTDC_GD_Unknown '0'
///��
#define THOST_FTDC_GD_Male '1'
///Ů
#define THOST_FTDC_GD_Female '2'

typedef char TThostFtdcGenderType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFeePayFlagType��һ������֧����־����
/////////////////////////////////////////////////////////////////////////
///�����淽֧������
#define THOST_FTDC_FPF_BEN '0'
///�ɷ��ͷ�֧������
#define THOST_FTDC_FPF_OUR '1'
///�ɷ��ͷ�֧������ķ��ã����淽֧�����ܵķ���
#define THOST_FTDC_FPF_SHA '2'

typedef char TThostFtdcFeePayFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPassWordKeyTypeType��һ����Կ��������
/////////////////////////////////////////////////////////////////////////
///������Կ
#define THOST_FTDC_PWKT_ExchangeKey '0'
///������Կ
#define THOST_FTDC_PWKT_PassWordKey '1'
///MAC��Կ
#define THOST_FTDC_PWKT_MACKey '2'
///������Կ
#define THOST_FTDC_PWKT_MessageKey '3'

typedef char TThostFtdcPassWordKeyTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBTPassWordTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///��ѯ
#define THOST_FTDC_PWT_Query '0'
///ȡ��
#define THOST_FTDC_PWT_Fetch '1'
///ת��
#define THOST_FTDC_PWT_Transfer '2'
///����
#define THOST_FTDC_PWT_Trade '3'

typedef char TThostFtdcFBTPassWordTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBTEncryModeType��һ�����ܷ�ʽ����
/////////////////////////////////////////////////////////////////////////
///������
#define THOST_FTDC_EM_NoEncry '0'
///DES
#define THOST_FTDC_EM_DES '1'
///3DES
#define THOST_FTDC_EM_3DES '2'

typedef char TThostFtdcFBTEncryModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBankRepealFlagType��һ�����г�����־����
/////////////////////////////////////////////////////////////////////////
///���������Զ�����
#define THOST_FTDC_BRF_BankNotNeedRepeal '0'
///���д��Զ�����
#define THOST_FTDC_BRF_BankWaitingRepeal '1'
///�������Զ�����
#define THOST_FTDC_BRF_BankBeenRepealed '2'

typedef char TThostFtdcBankRepealFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBrokerRepealFlagType��һ�����̳�����־����
/////////////////////////////////////////////////////////////////////////
///���������Զ�����
#define THOST_FTDC_BRORF_BrokerNotNeedRepeal '0'
///���̴��Զ�����
#define THOST_FTDC_BRORF_BrokerWaitingRepeal '1'
///�������Զ�����
#define THOST_FTDC_BRORF_BrokerBeenRepealed '2'

typedef char TThostFtdcBrokerRepealFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcInstitutionTypeType��һ�������������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_TS_Bank '0'
///����
#define THOST_FTDC_TS_Future '1'
///ȯ��
#define THOST_FTDC_TS_Store '2'

typedef char TThostFtdcInstitutionTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcLastFragmentType��һ������Ƭ��־����
/////////////////////////////////////////////////////////////////////////
///������Ƭ
#define THOST_FTDC_LF_Yes '0'
///��������Ƭ
#define THOST_FTDC_LF_No '1'

typedef char TThostFtdcLastFragmentType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBankAccStatusType��һ�������˻�״̬����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_BAS_Normal '0'
///����
#define THOST_FTDC_BAS_Freeze '1'
///��ʧ
#define THOST_FTDC_BAS_ReportLoss '2'

typedef char TThostFtdcBankAccStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcMoneyAccountStatusType��һ���ʽ��˻�״̬����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_MAS_Normal '0'
///����
#define THOST_FTDC_MAS_Cancel '1'

typedef char TThostFtdcMoneyAccountStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcManageStatusType��һ�����״̬����
/////////////////////////////////////////////////////////////////////////
///ָ�����
#define THOST_FTDC_MSS_Point '0'
///Ԥָ��
#define THOST_FTDC_MSS_PrePoint '1'
///����ָ��
#define THOST_FTDC_MSS_CancelPoint '2'

typedef char TThostFtdcManageStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSystemTypeType��һ��Ӧ��ϵͳ��������
/////////////////////////////////////////////////////////////////////////
///����ת��
#define THOST_FTDC_SYT_FutureBankTransfer '0'
///��֤ת��
#define THOST_FTDC_SYT_StockBankTransfer '1'
///���������
#define THOST_FTDC_SYT_TheThirdPartStore '2'

typedef char TThostFtdcSystemTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTxnEndFlagType��һ������ת�ʻ�ת�����־����
/////////////////////////////////////////////////////////////////////////
///����������
#define THOST_FTDC_TEF_NormalProcessing '0'
///�ɹ�����
#define THOST_FTDC_TEF_Success '1'
///ʧ�ܽ���
#define THOST_FTDC_TEF_Failed '2'
///�쳣��
#define THOST_FTDC_TEF_Abnormal '3'
///���˹��쳣����
#define THOST_FTDC_TEF_ManualProcessedForException '4'
///ͨѶ�쳣 �����˹�����
#define THOST_FTDC_TEF_CommuFailedNeedManualProcess '5'
///ϵͳ�������˹�����
#define THOST_FTDC_TEF_SysErrorNeedManualProcess '6'

typedef char TThostFtdcTxnEndFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcProcessStatusType��һ������ת�ʷ�����״̬����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_PSS_NotProcess '0'
///��ʼ����
#define THOST_FTDC_PSS_StartProcess '1'
///�������
#define THOST_FTDC_PSS_Finished '2'

typedef char TThostFtdcProcessStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCustTypeType��һ���ͻ���������
/////////////////////////////////////////////////////////////////////////
///��Ȼ��
#define THOST_FTDC_CUSTT_Person '0'
///������
#define THOST_FTDC_CUSTT_Institution '1'

typedef char TThostFtdcCustTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBTTransferDirectionType��һ������ת�ʷ�������
/////////////////////////////////////////////////////////////////////////
///�������ת�ڻ�
#define THOST_FTDC_FBTTD_FromBankToFuture '1'
///�����ڻ�ת����
#define THOST_FTDC_FBTTD_FromFutureToBank '2'

typedef char TThostFtdcFBTTransferDirectionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOpenOrDestroyType��һ���������������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_OOD_Open '1'
///����
#define THOST_FTDC_OOD_Destroy '0'

typedef char TThostFtdcOpenOrDestroyType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAvailabilityFlagType��һ����Ч��־����
/////////////////////////////////////////////////////////////////////////
///δȷ��
#define THOST_FTDC_AVAF_Invalid '0'
///��Ч
#define THOST_FTDC_AVAF_Valid '1'
///����
#define THOST_FTDC_AVAF_Repeal '2'

typedef char TThostFtdcAvailabilityFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOrganTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///���д���
#define THOST_FTDC_OT_Bank '1'
///����ǰ��
#define THOST_FTDC_OT_Future '2'
///����ת��ƽ̨����
#define THOST_FTDC_OT_PlateForm '9'

typedef char TThostFtdcOrganTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOrganLevelType��һ��������������
/////////////////////////////////////////////////////////////////////////
///�������л������ܲ�
#define THOST_FTDC_OL_HeadQuarters '1'
///���з����Ļ��ڻ���˾Ӫҵ��
#define THOST_FTDC_OL_Branch '2'

typedef char TThostFtdcOrganLevelType;

/////////////////////////////////////////////////////////////////////////
///TFtdcProtocalIDType��һ��Э����������
/////////////////////////////////////////////////////////////////////////
///����Э��
#define THOST_FTDC_PID_FutureProtocal '0'
///����Э��
#define THOST_FTDC_PID_ICBCProtocal '1'
///ũ��Э��
#define THOST_FTDC_PID_ABCProtocal '2'
///�й�����Э��
#define THOST_FTDC_PID_CBCProtocal '3'
///����Э��
#define THOST_FTDC_PID_CCBProtocal '4'
///����Э��
#define THOST_FTDC_PID_BOCOMProtocal '5'
///����ת��ƽ̨Э��
#define THOST_FTDC_PID_FBTPlateFormProtocal 'X'

typedef char TThostFtdcProtocalIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcConnectModeType��һ���׽������ӷ�ʽ����
/////////////////////////////////////////////////////////////////////////
///������
#define THOST_FTDC_CM_ShortConnect '0'
///������
#define THOST_FTDC_CM_LongConnect '1'

typedef char TThostFtdcConnectModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSyncModeType��һ���׽���ͨ�ŷ�ʽ����
/////////////////////////////////////////////////////////////////////////
///�첽
#define THOST_FTDC_SRM_ASync '0'
///ͬ��
#define THOST_FTDC_SRM_Sync '1'

typedef char TThostFtdcSyncModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBankAccTypeType��һ�������ʺ���������
/////////////////////////////////////////////////////////////////////////
///���д���
#define THOST_FTDC_BAT_BankBook '1'
///���
#define THOST_FTDC_BAT_SavingCard '2'
///���ÿ�
#define THOST_FTDC_BAT_CreditCard '3'

typedef char TThostFtdcBankAccTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFutureAccTypeType��һ���ڻ���˾�ʺ���������
/////////////////////////////////////////////////////////////////////////
///���д���
#define THOST_FTDC_FAT_BankBook '1'
///���
#define THOST_FTDC_FAT_SavingCard '2'
///���ÿ�
#define THOST_FTDC_FAT_CreditCard '3'

typedef char TThostFtdcFutureAccTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOrganStatusType��һ���������״̬����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_OS_Ready '0'
///ǩ��
#define THOST_FTDC_OS_CheckIn '1'
///ǩ��
#define THOST_FTDC_OS_CheckOut '2'
///�����ļ�����
#define THOST_FTDC_OS_CheckFileArrived '3'
///����
#define THOST_FTDC_OS_CheckDetail '4'
///��������
#define THOST_FTDC_OS_DayEndClean '5'
///ע��
#define THOST_FTDC_OS_Invalid '9'

typedef char TThostFtdcOrganStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCCBFeeModeType��һ�������շ�ģʽ����
/////////////////////////////////////////////////////////////////////////
///��������
#define THOST_FTDC_CCBFM_ByAmount '1'
///���¿���
#define THOST_FTDC_CCBFM_ByMonth '2'

typedef char TThostFtdcCCBFeeModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCommApiTypeType��һ��ͨѶAPI��������
/////////////////////////////////////////////////////////////////////////
///�ͻ���
#define THOST_FTDC_CAPIT_Client '1'
///�����
#define THOST_FTDC_CAPIT_Server '2'
///����ϵͳ��UserApi
#define THOST_FTDC_CAPIT_UserApi '3'

typedef char TThostFtdcCommApiTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcServiceIDType��һ������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcServiceIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcServiceLineNoType��һ��������·�������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcServiceLineNoType;

/////////////////////////////////////////////////////////////////////////
///TFtdcServiceNameType��һ������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcServiceNameType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcLinkStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///�Ѿ�����
#define THOST_FTDC_LS_Connected '1'
///û������
#define THOST_FTDC_LS_Disconnected '2'

typedef char TThostFtdcLinkStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCommApiPointerType��һ��ͨѶAPIָ������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcCommApiPointerType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPwdFlagType��һ������˶Ա�־����
/////////////////////////////////////////////////////////////////////////
///���˶�
#define THOST_FTDC_BPWDF_NoCheck '0'
///���ĺ˶�
#define THOST_FTDC_BPWDF_BlankCheck '1'
///���ĺ˶�
#define THOST_FTDC_BPWDF_EncryptCheck '2'

typedef char TThostFtdcPwdFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSecuAccTypeType��һ���ڻ��ʺ���������
/////////////////////////////////////////////////////////////////////////
///�ʽ��ʺ�
#define THOST_FTDC_SAT_AccountID '1'
///�ʽ𿨺�
#define THOST_FTDC_SAT_CardID '2'
///�Ϻ��ɶ��ʺ�
#define THOST_FTDC_SAT_SHStockholderID '3'
///���ڹɶ��ʺ�
#define THOST_FTDC_SAT_SZStockholderID '4'

typedef char TThostFtdcSecuAccTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTransferStatusType��һ��ת�˽���״̬����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_TRFS_Normal '0'
///������
#define THOST_FTDC_TRFS_Repealed '1'

typedef char TThostFtdcTransferStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSponsorTypeType��һ����������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_SPTYPE_Broker '0'
///����
#define THOST_FTDC_SPTYPE_Bank '1'

typedef char TThostFtdcSponsorTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcReqRspTypeType��һ��������Ӧ�������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_REQRSP_Request '0'
///��Ӧ
#define THOST_FTDC_REQRSP_Response '1'

typedef char TThostFtdcReqRspTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBTUserEventTypeType��һ������ת���û��¼���������
/////////////////////////////////////////////////////////////////////////
///ǩ��
#define THOST_FTDC_FBTUET_SignIn '0'
///����ת�ڻ�
#define THOST_FTDC_FBTUET_FromBankToFuture '1'
///�ڻ�ת����
#define THOST_FTDC_FBTUET_FromFutureToBank '2'
///����
#define THOST_FTDC_FBTUET_OpenAccount '3'
///����
#define THOST_FTDC_FBTUET_CancelAccount '4'
///��������˻�
#define THOST_FTDC_FBTUET_ChangeAccount '5'
///��������ת�ڻ�
#define THOST_FTDC_FBTUET_RepealFromBankToFuture '6'
///�����ڻ�ת����
#define THOST_FTDC_FBTUET_RepealFromFutureToBank '7'
///��ѯ�����˻�
#define THOST_FTDC_FBTUET_QueryBankAccount '8'
///��ѯ�ڻ��˻�
#define THOST_FTDC_FBTUET_QueryFutureAccount '9'
///ǩ��
#define THOST_FTDC_FBTUET_SignOut 'A'
///��Կͬ��
#define THOST_FTDC_FBTUET_SyncKey 'B'
///ԤԼ����
#define THOST_FTDC_FBTUET_ReserveOpenAccount 'C'
///����ԤԼ����
#define THOST_FTDC_FBTUET_CancelReserveOpenAccount 'D'
///ԤԼ����ȷ��
#define THOST_FTDC_FBTUET_ReserveOpenAccountConfirm 'E'
///����
#define THOST_FTDC_FBTUET_Other 'Z'

typedef char TThostFtdcFBTUserEventTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBankIDByBankType��һ�������Լ��ı�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankIDByBankType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankOperNoType��һ�����в���Ա������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankOperNoType[4];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankCustNoType��һ�����пͻ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankCustNoType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcDBOPSeqNoType��һ�����������к�����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcDBOPSeqNoType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTableNameType��һ��FBT��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcTableNameType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcPKNameType��һ��FBT���������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPKNameType[201];

/////////////////////////////////////////////////////////////////////////
///TFtdcPKValueType��һ��FBT���������ֵ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPKValueType[501];

/////////////////////////////////////////////////////////////////////////
///TFtdcDBOperationType��һ����¼������������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_DBOP_Insert '0'
///����
#define THOST_FTDC_DBOP_Update '1'
///ɾ��
#define THOST_FTDC_DBOP_Delete '2'

typedef char TThostFtdcDBOperationType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSyncFlagType��һ��ͬ���������
/////////////////////////////////////////////////////////////////////////
///��ͬ��
#define THOST_FTDC_SYNF_Yes '0'
///δͬ��
#define THOST_FTDC_SYNF_No '1'

typedef char TThostFtdcSyncFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTargetIDType��һ��ͬ��Ŀ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcTargetIDType[4];

/////////////////////////////////////////////////////////////////////////
///TFtdcSyncTypeType��һ��ͬ����������
/////////////////////////////////////////////////////////////////////////
///һ��ͬ��
#define THOST_FTDC_SYNT_OneOffSync '0'
///��ʱͬ��
#define THOST_FTDC_SYNT_TimerSync '1'
///��ʱ��ȫͬ��
#define THOST_FTDC_SYNT_TimerFullSync '2'

typedef char TThostFtdcSyncTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBETimeType��һ�����ֻ���ʱ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBETimeType[7];

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEBankNoType��һ�����������к�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBEBankNoType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcFBECertNoType��һ������ƾ֤������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBECertNoType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcExDirectionType��һ�����㷽������
/////////////////////////////////////////////////////////////////////////
///���
#define THOST_FTDC_FBEDIR_Settlement '0'
///�ۻ�
#define THOST_FTDC_FBEDIR_Sale '1'

typedef char TThostFtdcExDirectionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEBankAccountType��һ�����������˻�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBEBankAccountType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEBankAccountNameType��һ�����������˻�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBEBankAccountNameType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEAmtType��һ�����ֻ���������
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcFBEAmtType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEBusinessTypeType��һ������ҵ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBEBusinessTypeType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEPostScriptType��һ�����㸽������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBEPostScriptType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcFBERemarkType��һ�����㱸ע����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBERemarkType[71];

/////////////////////////////////////////////////////////////////////////
///TFtdcExRateType��һ�������������
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcExRateType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEResultFlagType��һ������ɹ���־����
/////////////////////////////////////////////////////////////////////////
///�ɹ�
#define THOST_FTDC_FBERES_Success '0'
///�˻�����
#define THOST_FTDC_FBERES_InsufficientBalance '1'
///���׽��δ֪
#define THOST_FTDC_FBERES_UnknownTrading '8'
///ʧ��
#define THOST_FTDC_FBERES_Fail 'x'

typedef char TThostFtdcFBEResultFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBERtnMsgType��һ�����㷵����Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBERtnMsgType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEExtendMsgType��һ��������չ��Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBEExtendMsgType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEBusinessSerialType��һ�����������ˮ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBEBusinessSerialType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcFBESystemSerialType��һ��������ˮ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBESystemSerialType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcFBETotalExCntType��һ�����㽻���ܱ�������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcFBETotalExCntType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEExchStatusType��һ�����㽻��״̬����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_FBEES_Normal '0'
///�����ط�
#define THOST_FTDC_FBEES_ReExchange '1'

typedef char TThostFtdcFBEExchStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEFileFlagType��һ�������ļ���־����
/////////////////////////////////////////////////////////////////////////
///���ݰ�
#define THOST_FTDC_FBEFG_DataPackage '0'
///�ļ�
#define THOST_FTDC_FBEFG_File '1'

typedef char TThostFtdcFBEFileFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEAlreadyTradeType��һ�������ѽ��ױ�־����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_FBEAT_NotTrade '0'
///�ѽ���
#define THOST_FTDC_FBEAT_Trade '1'

typedef char TThostFtdcFBEAlreadyTradeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEOpenBankType��һ�������˻�����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBEOpenBankType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEUserEventTypeType��һ�����ڻ����û��¼���������
/////////////////////////////////////////////////////////////////////////
///ǩ��
#define THOST_FTDC_FBEUET_SignIn '0'
///����
#define THOST_FTDC_FBEUET_Exchange '1'
///�����ط�
#define THOST_FTDC_FBEUET_ReExchange '2'
///�����˻���ѯ
#define THOST_FTDC_FBEUET_QueryBankAccount '3'
///������ϸ��ѯ
#define THOST_FTDC_FBEUET_QueryExchDetial '4'
///������ܲ�ѯ
#define THOST_FTDC_FBEUET_QueryExchSummary '5'
///������ʲ�ѯ
#define THOST_FTDC_FBEUET_QueryExchRate '6'
///�����ļ�֪ͨ
#define THOST_FTDC_FBEUET_CheckBankAccount '7'
///ǩ��
#define THOST_FTDC_FBEUET_SignOut '8'
///����
#define THOST_FTDC_FBEUET_Other 'Z'

typedef char TThostFtdcFBEUserEventTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEFileNameType��һ����������ļ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBEFileNameType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEBatchSerialType��һ���������κ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFBEBatchSerialType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcFBEReqFlagType��һ�����㷢�ͱ�־����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_FBERF_UnProcessed '0'
///�ȴ�����
#define THOST_FTDC_FBERF_WaitSend '1'
///���ͳɹ�
#define THOST_FTDC_FBERF_SendSuccess '2'
///����ʧ��
#define THOST_FTDC_FBERF_SendFailed '3'
///�ȴ��ط�
#define THOST_FTDC_FBERF_WaitReSend '4'

typedef char TThostFtdcFBEReqFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcNotifyClassType��һ������֪ͨ��������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_NC_NOERROR '0'
///��ʾ
#define THOST_FTDC_NC_Warn '1'
///׷��
#define THOST_FTDC_NC_Call '2'
///ǿƽ
#define THOST_FTDC_NC_Force '3'
///����
#define THOST_FTDC_NC_CHUANCANG '4'
///�쳣
#define THOST_FTDC_NC_Exception '5'

typedef char TThostFtdcNotifyClassType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRiskNofityInfoType��һ���ͻ�����֪ͨ��Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRiskNofityInfoType[257];

/////////////////////////////////////////////////////////////////////////
///TFtdcForceCloseSceneIdType��һ��ǿƽ�����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcForceCloseSceneIdType[24];

/////////////////////////////////////////////////////////////////////////
///TFtdcForceCloseTypeType��һ��ǿƽ����������
/////////////////////////////////////////////////////////////////////////
///�ֹ�ǿƽ
#define THOST_FTDC_FCT_Manual '0'
///��һͶ���߸���ǿƽ
#define THOST_FTDC_FCT_Single '1'
///����Ͷ���߸���ǿƽ
#define THOST_FTDC_FCT_Group '2'

typedef char TThostFtdcForceCloseTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcInstrumentIDsType��һ�������Ʒ����,��+�ָ�,��cu+zn����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInstrumentIDsType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcRiskNotifyMethodType��һ������֪ͨ;������
/////////////////////////////////////////////////////////////////////////
///ϵͳ֪ͨ
#define THOST_FTDC_RNM_System '0'
///����֪ͨ
#define THOST_FTDC_RNM_SMS '1'
///�ʼ�֪ͨ
#define THOST_FTDC_RNM_EMail '2'
///�˹�֪ͨ
#define THOST_FTDC_RNM_Manual '3'

typedef char TThostFtdcRiskNotifyMethodType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRiskNotifyStatusType��һ������֪ͨ״̬����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_RNS_NotGen '0'
///������δ����
#define THOST_FTDC_RNS_Generated '1'
///����ʧ��
#define THOST_FTDC_RNS_SendError '2'
///�ѷ���δ����
#define THOST_FTDC_RNS_SendOk '3'
///�ѽ���δȷ��
#define THOST_FTDC_RNS_Received '4'
///��ȷ��
#define THOST_FTDC_RNS_Confirmed '5'

typedef char TThostFtdcRiskNotifyStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRiskUserEventType��һ������û������¼�����
/////////////////////////////////////////////////////////////////////////
///��������
#define THOST_FTDC_RUE_ExportData '0'

typedef char TThostFtdcRiskUserEventType;

/////////////////////////////////////////////////////////////////////////
///TFtdcParamIDType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcParamIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcParamNameType��һ������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcParamNameType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcParamValueType��һ������ֵ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcParamValueType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcConditionalOrderSortTypeType��һ��������������������
/////////////////////////////////////////////////////////////////////////
///ʹ�����¼�����
#define THOST_FTDC_COST_LastPriceAsc '0'
///ʹ�����¼۽���
#define THOST_FTDC_COST_LastPriceDesc '1'
///ʹ����������
#define THOST_FTDC_COST_AskPriceAsc '2'
///ʹ�����۽���
#define THOST_FTDC_COST_AskPriceDesc '3'
///ʹ���������
#define THOST_FTDC_COST_BidPriceAsc '4'
///ʹ����۽���
#define THOST_FTDC_COST_BidPriceDesc '5'

typedef char TThostFtdcConditionalOrderSortTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSendTypeType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_UOAST_NoSend '0'
///�ѷ���
#define THOST_FTDC_UOAST_Sended '1'
///������
#define THOST_FTDC_UOAST_Generated '2'
///����ʧ��
#define THOST_FTDC_UOAST_SendFail '3'
///���ճɹ�
#define THOST_FTDC_UOAST_Success '4'
///����ʧ��
#define THOST_FTDC_UOAST_Fail '5'
///ȡ������
#define THOST_FTDC_UOAST_Cancel '6'

typedef char TThostFtdcSendTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcClientIDStatusType��һ�����ױ���״̬����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_UOACS_NoApply '1'
///���ύ����
#define THOST_FTDC_UOACS_Submited '2'
///�ѷ�������
#define THOST_FTDC_UOACS_Sended '3'
///���
#define THOST_FTDC_UOACS_Success '4'
///�ܾ�
#define THOST_FTDC_UOACS_Refuse '5'
///�ѳ�������
#define THOST_FTDC_UOACS_Cancel '6'

typedef char TThostFtdcClientIDStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcIndustryIDType��һ����ҵ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcIndustryIDType[17];

/////////////////////////////////////////////////////////////////////////
///TFtdcQuestionIDType��һ��������Ϣ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcQuestionIDType[5];

/////////////////////////////////////////////////////////////////////////
///TFtdcQuestionContentType��һ��������Ϣ˵������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcQuestionContentType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcOptionIDType��һ��ѡ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOptionIDType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcOptionContentType��һ��ѡ��˵������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOptionContentType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcQuestionTypeType��һ��������Ϣ��������
/////////////////////////////////////////////////////////////////////////
///��ѡ
#define THOST_FTDC_QT_Radio '1'
///��ѡ
#define THOST_FTDC_QT_Option '2'
///���
#define THOST_FTDC_QT_Blank '3'

typedef char TThostFtdcQuestionTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcProcessIDType��һ��ҵ����ˮ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcProcessIDType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcSeqNoType��һ����ˮ������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcSeqNoType;

/////////////////////////////////////////////////////////////////////////
///TFtdcUOAProcessStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUOAProcessStatusType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcProcessTypeType��һ�����̹�����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcProcessTypeType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcBusinessTypeType��һ��ҵ����������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_BT_Request '1'
///Ӧ��
#define THOST_FTDC_BT_Response '2'
///֪ͨ
#define THOST_FTDC_BT_Notice '3'

typedef char TThostFtdcBusinessTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCfmmcReturnCodeType��һ��������ķ���������
/////////////////////////////////////////////////////////////////////////
///�ɹ�
#define THOST_FTDC_CRC_Success '0'
///�ÿͻ��Ѿ��������ڴ�����
#define THOST_FTDC_CRC_Working '1'
///����пͻ����ϼ��ʧ��
#define THOST_FTDC_CRC_InfoFail '2'
///�����ʵ���Ƽ��ʧ��
#define THOST_FTDC_CRC_IDCardFail '3'
///��������
#define THOST_FTDC_CRC_OtherFail '4'

typedef char TThostFtdcCfmmcReturnCodeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcExReturnCodeType��һ������������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcExReturnCodeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcClientTypeType��һ���ͻ���������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_CfMMCCT_All '0'
///����
#define THOST_FTDC_CfMMCCT_Person '1'
///��λ
#define THOST_FTDC_CfMMCCT_Company '2'
///����
#define THOST_FTDC_CfMMCCT_Other '3'
///���ⷨ��
#define THOST_FTDC_CfMMCCT_SpecialOrgan '4'
///�ʹܻ�
#define THOST_FTDC_CfMMCCT_Asset '5'

typedef char TThostFtdcClientTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcExchangeIDTypeType��һ���������������
/////////////////////////////////////////////////////////////////////////
///�Ϻ��ڻ�������
#define THOST_FTDC_EIDT_SHFE 'S'
///֣����Ʒ������
#define THOST_FTDC_EIDT_CZCE 'Z'
///������Ʒ������
#define THOST_FTDC_EIDT_DCE 'D'
///�й������ڻ�������
#define THOST_FTDC_EIDT_CFFEX 'J'
///�Ϻ�������Դ�������Ĺɷ����޹�˾
#define THOST_FTDC_EIDT_INE 'N'

typedef char TThostFtdcExchangeIDTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcExClientIDTypeType��һ�����ױ�����������
/////////////////////////////////////////////////////////////////////////
///�ױ�
#define THOST_FTDC_ECIDT_Hedge '1'
///����
#define THOST_FTDC_ECIDT_Arbitrage '2'
///Ͷ��
#define THOST_FTDC_ECIDT_Speculation '3'

typedef char TThostFtdcExClientIDTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcClientClassifyType��һ���ͻ�����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcClientClassifyType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcUOAOrganTypeType��һ����λ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUOAOrganTypeType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcUOACountryCodeType��һ�����Ҵ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUOACountryCodeType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcAreaCodeType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAreaCodeType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcFuturesIDType��һ���������Ϊ�ͻ�����Ĵ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFuturesIDType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcCffmcDateType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCffmcDateType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcCffmcTimeType��һ��ʱ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCffmcTimeType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcNocIDType��һ����֯������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcNocIDType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcUpdateFlagType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_UF_NoUpdate '0'
///����ȫ����Ϣ�ɹ�
#define THOST_FTDC_UF_Success '1'
///����ȫ����Ϣʧ��
#define THOST_FTDC_UF_Fail '2'
///���½��ױ���ɹ�
#define THOST_FTDC_UF_TCSuccess '3'
///���½��ױ���ʧ��
#define THOST_FTDC_UF_TCFail '4'
///�Ѷ���
#define THOST_FTDC_UF_Cancel '5'

typedef char TThostFtdcUpdateFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcApplyOperateIDType��һ�����붯������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_AOID_OpenInvestor '1'
///�޸������Ϣ
#define THOST_FTDC_AOID_ModifyIDCard '2'
///�޸�һ����Ϣ
#define THOST_FTDC_AOID_ModifyNoIDCard '3'
///���뽻�ױ���
#define THOST_FTDC_AOID_ApplyTradingCode '4'
///�������ױ���
#define THOST_FTDC_AOID_CancelTradingCode '5'
///����
#define THOST_FTDC_AOID_CancelInvestor '6'
///�˻�����
#define THOST_FTDC_AOID_FreezeAccount '8'
///���������˻�
#define THOST_FTDC_AOID_ActiveFreezeAccount '9'

typedef char TThostFtdcApplyOperateIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcApplyStatusIDType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///δ��ȫ
#define THOST_FTDC_ASID_NoComplete '1'
///���ύ
#define THOST_FTDC_ASID_Submited '2'
///�����
#define THOST_FTDC_ASID_Checked '3'
///�Ѿܾ�
#define THOST_FTDC_ASID_Refused '4'
///��ɾ��
#define THOST_FTDC_ASID_Deleted '5'

typedef char TThostFtdcApplyStatusIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSendMethodType��һ�����ͷ�ʽ����
/////////////////////////////////////////////////////////////////////////
///�ļ�����
#define THOST_FTDC_UOASM_ByAPI '1'
///���ӷ���
#define THOST_FTDC_UOASM_ByFile '2'

typedef char TThostFtdcSendMethodType;

/////////////////////////////////////////////////////////////////////////
///TFtdcEventTypeType��һ��ҵ�������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcEventTypeType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcEventModeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_EvM_ADD '1'
///�޸�
#define THOST_FTDC_EvM_UPDATE '2'
///ɾ��
#define THOST_FTDC_EvM_DELETE '3'
///����
#define THOST_FTDC_EvM_CHECK '4'
///����
#define THOST_FTDC_EvM_COPY '5'
///ע��
#define THOST_FTDC_EvM_CANCEL '6'
///����
#define THOST_FTDC_EvM_Reverse '7'

typedef char TThostFtdcEventModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcUOAAutoSendType��һ��ͳһ���������Զ���������
/////////////////////////////////////////////////////////////////////////
///�Զ����Ͳ�����
#define THOST_FTDC_UOAA_ASR '1'
///�Զ����ͣ����Զ�����
#define THOST_FTDC_UOAA_ASNR '2'
///���Զ����ͣ��Զ�����
#define THOST_FTDC_UOAA_NSAR '3'
///���Զ����ͣ�Ҳ���Զ�����
#define THOST_FTDC_UOAA_NSR '4'

typedef char TThostFtdcUOAAutoSendType;

/////////////////////////////////////////////////////////////////////////
///TFtdcQueryDepthType��һ����ѯ�������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcQueryDepthType;

/////////////////////////////////////////////////////////////////////////
///TFtdcDataCenterIDType��һ���������Ĵ�������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcDataCenterIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFlowIDType��һ������ID����
/////////////////////////////////////////////////////////////////////////
///Ͷ���߶�ӦͶ����������
#define THOST_FTDC_EvM_InvestorGroupFlow '1'
///Ͷ����������������
#define THOST_FTDC_EvM_InvestorRate '2'
///Ͷ������������ģ���ϵ����
#define THOST_FTDC_EvM_InvestorCommRateModel '3'

typedef char TThostFtdcFlowIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCheckLevelType��һ�����˼�������
/////////////////////////////////////////////////////////////////////////
///�㼶����
#define THOST_FTDC_CL_Zero '0'
///һ������
#define THOST_FTDC_CL_One '1'
///��������
#define THOST_FTDC_CL_Two '2'

typedef char TThostFtdcCheckLevelType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCheckNoType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcCheckNoType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCheckStatusType��һ�����˼�������
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_CHS_Init '0'
///������
#define THOST_FTDC_CHS_Checking '1'
///�Ѹ���
#define THOST_FTDC_CHS_Checked '2'
///�ܾ�
#define THOST_FTDC_CHS_Refuse '3'
///����
#define THOST_FTDC_CHS_Cancel '4'

typedef char TThostFtdcCheckStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcUsedStatusType��һ����Ч״̬����
/////////////////////////////////////////////////////////////////////////
///δ��Ч
#define THOST_FTDC_CHU_Unused '0'
///����Ч
#define THOST_FTDC_CHU_Used '1'
///��Чʧ��
#define THOST_FTDC_CHU_Fail '2'

typedef char TThostFtdcUsedStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRateTemplateNameType��һ��ģ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRateTemplateNameType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcPropertyStringType��һ�����ڲ�ѯ��Ͷ�������ֶ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPropertyStringType[2049];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankAcountOriginType��һ���˻���Դ����
/////////////////////////////////////////////////////////////////////////
///�ֹ�¼��
#define THOST_FTDC_BAO_ByAccProperty '0'
///����ת��
#define THOST_FTDC_BAO_ByFBTransfer '1'

typedef char TThostFtdcBankAcountOriginType;

/////////////////////////////////////////////////////////////////////////
///TFtdcMonthBillTradeSumType��һ�����㵥�±��ɽ����ܷ�ʽ����
/////////////////////////////////////////////////////////////////////////
///ͬ��ͬ��Լ
#define THOST_FTDC_MBTS_ByInstrument '0'
///ͬ��ͬ��Լͬ�۸�
#define THOST_FTDC_MBTS_ByDayInsPrc '1'
///ͬ��Լ
#define THOST_FTDC_MBTS_ByDayIns '2'

typedef char TThostFtdcMonthBillTradeSumType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFBTTradeCodeEnumType��һ�����ڽ��״���ö������
/////////////////////////////////////////////////////////////////////////
///���з�������ת�ڻ�
#define THOST_FTDC_FTC_BankLaunchBankToBroker '102001'
///�ڻ���������ת�ڻ�
#define THOST_FTDC_FTC_BrokerLaunchBankToBroker '202001'
///���з����ڻ�ת����
#define THOST_FTDC_FTC_BankLaunchBrokerToBank '102002'
///�ڻ������ڻ�ת����
#define THOST_FTDC_FTC_BrokerLaunchBrokerToBank '202002'

typedef char TThostFtdcFBTTradeCodeEnumType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRateTemplateIDType��һ��ģ�ʹ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRateTemplateIDType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcRiskRateType��һ�����ն�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRiskRateType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcTimestampType��һ��ʱ�������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcTimestampType;

/////////////////////////////////////////////////////////////////////////
///TFtdcInvestorIDRuleNameType��һ���Ŷι�����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInvestorIDRuleNameType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcInvestorIDRuleExprType��һ���Ŷι�����ʽ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInvestorIDRuleExprType[513];

/////////////////////////////////////////////////////////////////////////
///TFtdcLastDriftType��һ���ϴ�OTPƯ��ֵ����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcLastDriftType;

/////////////////////////////////////////////////////////////////////////
///TFtdcLastSuccessType��һ���ϴ�OTP�ɹ�ֵ����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcLastSuccessType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAuthKeyType��һ��������Կ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAuthKeyType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcSerialNumberType��һ�����к�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSerialNumberType[17];

/////////////////////////////////////////////////////////////////////////
///TFtdcOTPTypeType��һ����̬������������
/////////////////////////////////////////////////////////////////////////
///�޶�̬����
#define THOST_FTDC_OTP_NONE '0'
///ʱ������
#define THOST_FTDC_OTP_TOTP '1'

typedef char TThostFtdcOTPTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOTPVendorsIDType��һ����̬�����ṩ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOTPVendorsIDType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcOTPVendorsNameType��һ����̬�����ṩ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOTPVendorsNameType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcOTPStatusType��һ����̬����״̬����
/////////////////////////////////////////////////////////////////////////
///δʹ��
#define THOST_FTDC_OTPS_Unused '0'
///��ʹ��
#define THOST_FTDC_OTPS_Used '1'
///ע��
#define THOST_FTDC_OTPS_Disuse '2'

typedef char TThostFtdcOTPStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBrokerUserTypeType��һ�����ù�˾�û���������
/////////////////////////////////////////////////////////////////////////
///Ͷ����
#define THOST_FTDC_BUT_Investor '1'
///����Ա
#define THOST_FTDC_BUT_BrokerUser '2'

typedef char TThostFtdcBrokerUserTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFutureTypeType��һ���ڻ���������
/////////////////////////////////////////////////////////////////////////
///��Ʒ�ڻ�
#define THOST_FTDC_FUTT_Commodity '1'
///�����ڻ�
#define THOST_FTDC_FUTT_Financial '2'

typedef char TThostFtdcFutureTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFundEventTypeType��һ���ʽ���������������
/////////////////////////////////////////////////////////////////////////
///ת���޶�
#define THOST_FTDC_FET_Restriction '0'
///����ת���޶�
#define THOST_FTDC_FET_TodayRestriction '1'
///������ˮ
#define THOST_FTDC_FET_Transfer '2'
///�ʽ𶳽�
#define THOST_FTDC_FET_Credit '3'
///Ͷ���߿����ʽ����
#define THOST_FTDC_FET_InvestorWithdrawAlm '4'
///���������ʻ�ת���޶�
#define THOST_FTDC_FET_BankRestriction '5'
///����ǩԼ�˻�
#define THOST_FTDC_FET_Accountregister '6'
///�����������
#define THOST_FTDC_FET_ExchangeFundIO '7'
///Ͷ���߳����
#define THOST_FTDC_FET_InvestorFundIO '8'

typedef char TThostFtdcFundEventTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAccountSourceTypeType��һ���ʽ��˻���Դ����
/////////////////////////////////////////////////////////////////////////
///����ͬ��
#define THOST_FTDC_AST_FBTransfer '0'
///�ֹ�¼��
#define THOST_FTDC_AST_ManualEntry '1'

typedef char TThostFtdcAccountSourceTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCodeSourceTypeType��һ�����ױ�����Դ����
/////////////////////////////////////////////////////////////////////////
///ͳһ����(�ѹ淶)
#define THOST_FTDC_CST_UnifyAccount '0'
///�ֹ�¼��(δ�淶)
#define THOST_FTDC_CST_ManualEntry '1'

typedef char TThostFtdcCodeSourceTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcUserRangeType��һ������Ա��Χ����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_UR_All '0'
///��һ����Ա
#define THOST_FTDC_UR_Single '1'

typedef char TThostFtdcUserRangeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTimeSpanType��һ��ʱ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcTimeSpanType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcImportSequenceIDType��һ����̬���Ƶ������α������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcImportSequenceIDType[17];

/////////////////////////////////////////////////////////////////////////
///TFtdcByGroupType��һ������ͳ�Ʊ��ͻ�ͳ�Ʒ�ʽ����
/////////////////////////////////////////////////////////////////////////
///��Ͷ����ͳ��
#define THOST_FTDC_BG_Investor '2'
///����ͳ��
#define THOST_FTDC_BG_Group '1'

typedef char TThostFtdcByGroupType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTradeSumStatModeType��һ������ͳ�Ʊ���Χͳ�Ʒ�ʽ����
/////////////////////////////////////////////////////////////////////////
///����Լͳ��
#define THOST_FTDC_TSSM_Instrument '1'
///����Ʒͳ��
#define THOST_FTDC_TSSM_Product '2'
///��������ͳ��
#define THOST_FTDC_TSSM_Exchange '3'

typedef char TThostFtdcTradeSumStatModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcComTypeType��һ����ϳɽ���������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcComTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcUserProductIDType��һ����Ʒ��ʶ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUserProductIDType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcUserProductNameType��һ����Ʒ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUserProductNameType[65];

/////////////////////////////////////////////////////////////////////////
///TFtdcUserProductMemoType��һ����Ʒ˵������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUserProductMemoType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCCancelFlagType��һ������������־����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCCancelFlagType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCDateType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCDateType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCInvestorNameType��һ���ͻ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCInvestorNameType[201];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCOpenInvestorNameType��һ���ͻ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCOpenInvestorNameType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCInvestorIDType��һ���ͻ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCInvestorIDType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCIdentifiedCardNoType��һ��֤����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCIdentifiedCardNoType[51];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCClientIDType��һ�����ױ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCClientIDType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCBankFlagType��һ�����б�ʶ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCBankFlagType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCBankAccountType��һ�������˻�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCBankAccountType[23];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCOpenNameType��һ������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCOpenNameType[401];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCMemoType��һ��˵������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCMemoType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCTimeType��һ��ʱ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCTimeType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCTradeIDType��һ���ɽ���ˮ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCTradeIDType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCExchangeInstIDType��һ����Լ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCExchangeInstIDType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCMortgageNameType��һ����ѺƷ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCMortgageNameType[7];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCReasonType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCReasonType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcIsSettlementType��һ���Ƿ�Ϊ�ǽ����Ա����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcIsSettlementType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCMoneyType��һ���ʽ�����
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcCSRCMoneyType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCPriceType��һ���۸�����
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcCSRCPriceType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCOptionsTypeType��һ����Ȩ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCOptionsTypeType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCStrikePriceType��һ��ִ�м�����
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcCSRCStrikePriceType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCTargetProductIDType��һ�����Ʒ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCTargetProductIDType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCTargetInstrIDType��һ����ĺ�Լ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCTargetInstrIDType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcCommModelNameType��һ����������ģ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCommModelNameType[161];

/////////////////////////////////////////////////////////////////////////
///TFtdcCommModelMemoType��һ����������ģ�屸ע����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCommModelMemoType[1025];

/////////////////////////////////////////////////////////////////////////
///TFtdcExprSetModeType��һ�����ڱ��ʽ������������
/////////////////////////////////////////////////////////////////////////
///������й�������
#define THOST_FTDC_ESM_Relative '1'
///��������
#define THOST_FTDC_ESM_Typical '2'

typedef char TThostFtdcExprSetModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRateInvestorRangeType��һ��Ͷ���߷�Χ����
/////////////////////////////////////////////////////////////////////////
///��˾��׼
#define THOST_FTDC_RIR_All '1'
///ģ��
#define THOST_FTDC_RIR_Model '2'
///��һͶ����
#define THOST_FTDC_RIR_Single '3'

typedef char TThostFtdcRateInvestorRangeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAgentBrokerIDType��һ�������͹�˾��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAgentBrokerIDType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcDRIdentityIDType��һ���������Ĵ�������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcDRIdentityIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcDRIdentityNameType��һ������������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcDRIdentityNameType[65];

/////////////////////////////////////////////////////////////////////////
///TFtdcDBLinkIDType��һ��DBLink��ʶ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcDBLinkIDType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcSyncDataStatusType��һ��������ϵͳ����ͬ��״̬����
/////////////////////////////////////////////////////////////////////////
///δͬ��
#define THOST_FTDC_SDS_Initialize '0'
///ͬ����
#define THOST_FTDC_SDS_Settlementing '1'
///��ͬ��
#define THOST_FTDC_SDS_Settlemented '2'

typedef char TThostFtdcSyncDataStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTradeSourceType��һ���ɽ���Դ����
/////////////////////////////////////////////////////////////////////////
///���Խ�������ͨ�ر�
#define THOST_FTDC_TSRC_NORMAL '0'
///���Բ�ѯ
#define THOST_FTDC_TSRC_QUERY '1'

typedef char TThostFtdcTradeSourceType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFlexStatModeType��һ����Ʒ��Լͳ�Ʒ�ʽ����
/////////////////////////////////////////////////////////////////////////
///��Ʒͳ��
#define THOST_FTDC_FSM_Product '1'
///������ͳ��
#define THOST_FTDC_FSM_Exchange '2'
///ͳ������
#define THOST_FTDC_FSM_All '3'

typedef char TThostFtdcFlexStatModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcByInvestorRangeType��һ��Ͷ���߷�Χͳ�Ʒ�ʽ����
/////////////////////////////////////////////////////////////////////////
///����ͳ��
#define THOST_FTDC_BIR_Property '1'
///ͳ������
#define THOST_FTDC_BIR_All '2'

typedef char TThostFtdcByInvestorRangeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSRiskRateType��һ�����ն�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSRiskRateType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcSequenceNo12Type��һ���������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcSequenceNo12Type;

/////////////////////////////////////////////////////////////////////////
///TFtdcPropertyInvestorRangeType��һ��Ͷ���߷�Χ����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_PIR_All '1'
///Ͷ��������
#define THOST_FTDC_PIR_Property '2'
///��һͶ����
#define THOST_FTDC_PIR_Single '3'

typedef char TThostFtdcPropertyInvestorRangeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFileStatusType��һ���ļ�״̬����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_FIS_NoCreate '0'
///������
#define THOST_FTDC_FIS_Created '1'
///����ʧ��
#define THOST_FTDC_FIS_Failed '2'

typedef char TThostFtdcFileStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFileGenStyleType��һ���ļ����ɷ�ʽ����
/////////////////////////////////////////////////////////////////////////
///�·�
#define THOST_FTDC_FGS_FileTransmit '0'
///����
#define THOST_FTDC_FGS_FileGen '1'

typedef char TThostFtdcFileGenStyleType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSysOperModeType��һ��ϵͳ��־������������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_SoM_Add '1'
///�޸�
#define THOST_FTDC_SoM_Update '2'
///ɾ��
#define THOST_FTDC_SoM_Delete '3'
///����
#define THOST_FTDC_SoM_Copy '4'
///����
#define THOST_FTDC_SoM_AcTive '5'
///ע��
#define THOST_FTDC_SoM_CanCel '6'
///����
#define THOST_FTDC_SoM_ReSet '7'

typedef char TThostFtdcSysOperModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSysOperTypeType��һ��ϵͳ��־������������
/////////////////////////////////////////////////////////////////////////
///�޸Ĳ���Ա����
#define THOST_FTDC_SoT_UpdatePassword '0'
///����Ա��֯�ܹ���ϵ
#define THOST_FTDC_SoT_UserDepartment '1'
///��ɫ����
#define THOST_FTDC_SoT_RoleManager '2'
///��ɫ��������
#define THOST_FTDC_SoT_RoleFunction '3'
///������������
#define THOST_FTDC_SoT_BaseParam '4'
///���ò���Ա
#define THOST_FTDC_SoT_SetUserID '5'
///�û���ɫ����
#define THOST_FTDC_SoT_SetUserRole '6'
///�û�IP����
#define THOST_FTDC_SoT_UserIpRestriction '7'
///��֯�ܹ�����
#define THOST_FTDC_SoT_DepartmentManager '8'
///��֯�ܹ����ѯ���ิ��
#define THOST_FTDC_SoT_DepartmentCopy '9'
///���ױ������
#define THOST_FTDC_SoT_Tradingcode 'A'
///Ͷ����״̬ά��
#define THOST_FTDC_SoT_InvestorStatus 'B'
///Ͷ����Ȩ�޹���
#define THOST_FTDC_SoT_InvestorAuthority 'C'
///��������
#define THOST_FTDC_SoT_PropertySet 'D'
///����Ͷ��������
#define THOST_FTDC_SoT_ReSetInvestorPasswd 'E'
///Ͷ���߸�����Ϣά��
#define THOST_FTDC_SoT_InvestorPersonalityInfo 'F'

typedef char TThostFtdcSysOperTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCDataQueyTypeType��һ���ϱ����ݲ�ѯ��������
/////////////////////////////////////////////////////////////////////////
///��ѯ��ǰ�����ձ��͵�����
#define THOST_FTDC_CSRCQ_Current '0'
///��ѯ��ʷ���͵Ĵ����͹�˾������
#define THOST_FTDC_CSRCQ_History '1'

typedef char TThostFtdcCSRCDataQueyTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFreezeStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///��Ծ
#define THOST_FTDC_FRS_Normal '1'
///����
#define THOST_FTDC_FRS_Freeze '0'

typedef char TThostFtdcFreezeStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcStandardStatusType��һ���淶״̬����
/////////////////////////////////////////////////////////////////////////
///�ѹ淶
#define THOST_FTDC_STST_Standard '0'
///δ�淶
#define THOST_FTDC_STST_NonStandard '1'

typedef char TThostFtdcStandardStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCFreezeStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCFreezeStatusType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcRightParamTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///���߻�
#define THOST_FTDC_RPT_Freeze '1'
///�������߻�
#define THOST_FTDC_RPT_FreezeActive '2'
///����Ȩ������
#define THOST_FTDC_RPT_OpenLimit '3'
///�������Ȩ������
#define THOST_FTDC_RPT_RelieveOpenLimit '4'

typedef char TThostFtdcRightParamTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRightTemplateIDType��һ��ģ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRightTemplateIDType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcRightTemplateNameType��һ��ģ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRightTemplateNameType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcDataStatusType��һ����ϴǮ��˱�����״̬����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_AMLDS_Normal '0'
///��ɾ��
#define THOST_FTDC_AMLDS_Deleted '1'

typedef char TThostFtdcDataStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAMLCheckStatusType��һ�����״̬����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_AMLCHS_Init '0'
///������
#define THOST_FTDC_AMLCHS_Checking '1'
///�Ѹ���
#define THOST_FTDC_AMLCHS_Checked '2'
///�ܾ��ϱ�
#define THOST_FTDC_AMLCHS_RefuseReport '3'

typedef char TThostFtdcAMLCheckStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAmlDateTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///�������
#define THOST_FTDC_AMLDT_DrawDay '0'
///��������
#define THOST_FTDC_AMLDT_TouchDay '1'

typedef char TThostFtdcAmlDateTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAmlCheckLevelType��һ����˼�������
/////////////////////////////////////////////////////////////////////////
///�㼶���
#define THOST_FTDC_AMLCL_CheckLevel0 '0'
///һ�����
#define THOST_FTDC_AMLCL_CheckLevel1 '1'
///�������
#define THOST_FTDC_AMLCL_CheckLevel2 '2'
///�������
#define THOST_FTDC_AMLCL_CheckLevel3 '3'

typedef char TThostFtdcAmlCheckLevelType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAmlCheckFlowType��һ����ϴǮ���ݳ�ȡ�����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAmlCheckFlowType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcDataTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcDataTypeType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcExportFileTypeType��һ�������ļ���������
/////////////////////////////////////////////////////////////////////////
///CSV
#define THOST_FTDC_EFT_CSV '0'
///Excel
#define THOST_FTDC_EFT_EXCEL '1'
///DBF
#define THOST_FTDC_EFT_DBF '2'

typedef char TThostFtdcExportFileTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSettleManagerTypeType��һ������������������
/////////////////////////////////////////////////////////////////////////
///����ǰ׼��
#define THOST_FTDC_SMT_Before '1'
///����
#define THOST_FTDC_SMT_Settlement '2'
///�����˶�
#define THOST_FTDC_SMT_After '3'
///�������
#define THOST_FTDC_SMT_Settlemented '4'

typedef char TThostFtdcSettleManagerTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSettleManagerIDType��һ���������ô�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSettleManagerIDType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcSettleManagerNameType��һ������������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSettleManagerNameType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcSettleManagerLevelType��һ���������õȼ�����
/////////////////////////////////////////////////////////////////////////
///��Ҫ
#define THOST_FTDC_SML_Must '1'
///����
#define THOST_FTDC_SML_Alarm '2'
///��ʾ
#define THOST_FTDC_SML_Prompt '3'
///�����
#define THOST_FTDC_SML_Ignore '4'

typedef char TThostFtdcSettleManagerLevelType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSettleManagerGroupType��һ��ģ���������
/////////////////////////////////////////////////////////////////////////
///�������˶�
#define THOST_FTDC_SMG_Exhcange '1'
///�ڲ��˶�
#define THOST_FTDC_SMG_ASP '2'
///�ϱ����ݺ˶�
#define THOST_FTDC_SMG_CSRC '3'

typedef char TThostFtdcSettleManagerGroupType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCheckResultMemoType��һ���˶Խ��˵������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCheckResultMemoType[1025];

/////////////////////////////////////////////////////////////////////////
///TFtdcFunctionUrlType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcFunctionUrlType[1025];

/////////////////////////////////////////////////////////////////////////
///TFtdcAuthInfoType��һ���ͻ�����֤��Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAuthInfoType[129];

/////////////////////////////////////////////////////////////////////////
///TFtdcAuthCodeType��һ���ͻ�����֤������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAuthCodeType[17];

/////////////////////////////////////////////////////////////////////////
///TFtdcLimitUseTypeType��һ����ֵ���ʹ����������
/////////////////////////////////////////////////////////////////////////
///���ظ�ʹ��
#define THOST_FTDC_LUT_Repeatable '1'
///�����ظ�ʹ��
#define THOST_FTDC_LUT_Unrepeatable '2'

typedef char TThostFtdcLimitUseTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcDataResourceType��һ��������Դ����
/////////////////////////////////////////////////////////////////////////
///��ϵͳ
#define THOST_FTDC_DAR_Settle '1'
///������
#define THOST_FTDC_DAR_Exchange '2'
///��������
#define THOST_FTDC_DAR_CSRC '3'

typedef char TThostFtdcDataResourceType;

/////////////////////////////////////////////////////////////////////////
///TFtdcMarginTypeType��һ����֤����������
/////////////////////////////////////////////////////////////////////////
///��������֤����
#define THOST_FTDC_MGT_ExchMarginRate '0'
///Ͷ���߱�֤����
#define THOST_FTDC_MGT_InstrMarginRate '1'
///Ͷ���߽��ױ�֤����
#define THOST_FTDC_MGT_InstrMarginRateTrade '2'

typedef char TThostFtdcMarginTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcActiveTypeType��һ����Ч��������
/////////////////////////////////////////////////////////////////////////
///��������Ч
#define THOST_FTDC_ACT_Intraday '1'
///������Ч
#define THOST_FTDC_ACT_Long '2'

typedef char TThostFtdcActiveTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcMarginRateTypeType��һ����ͻ��֤������������
/////////////////////////////////////////////////////////////////////////
///��������֤����
#define THOST_FTDC_MRT_Exchange '1'
///Ͷ���߱�֤����
#define THOST_FTDC_MRT_Investor '2'
///Ͷ���߽��ױ�֤����
#define THOST_FTDC_MRT_InvestorTrade '3'

typedef char TThostFtdcMarginRateTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBackUpStatusType��һ����������״̬����
/////////////////////////////////////////////////////////////////////////
///δ���ɱ�������
#define THOST_FTDC_BUS_UnBak '0'
///��������������
#define THOST_FTDC_BUS_BakUp '1'
///�����ɱ�������
#define THOST_FTDC_BUS_BakUped '2'
///��������ʧ��
#define THOST_FTDC_BUS_BakFail '3'

typedef char TThostFtdcBackUpStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcInitSettlementType��һ�������ʼ��״̬����
/////////////////////////////////////////////////////////////////////////
///�����ʼ��δ��ʼ
#define THOST_FTDC_SIS_UnInitialize '0'
///�����ʼ����
#define THOST_FTDC_SIS_Initialize '1'
///�����ʼ�����
#define THOST_FTDC_SIS_Initialized '2'

typedef char TThostFtdcInitSettlementType;

/////////////////////////////////////////////////////////////////////////
///TFtdcReportStatusType��һ��������������״̬����
/////////////////////////////////////////////////////////////////////////
///δ���ɱ�������
#define THOST_FTDC_SRS_NoCreate '0'
///��������������
#define THOST_FTDC_SRS_Create '1'
///�����ɱ�������
#define THOST_FTDC_SRS_Created '2'
///���ɱ�������ʧ��
#define THOST_FTDC_SRS_CreateFail '3'

typedef char TThostFtdcReportStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSaveStatusType��һ�����ݹ鵵״̬����
/////////////////////////////////////////////////////////////////////////
///�鵵δ���
#define THOST_FTDC_SSS_UnSaveData '0'
///�鵵���
#define THOST_FTDC_SSS_SaveDatad '1'

typedef char TThostFtdcSaveStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSettArchiveStatusType��һ������ȷ�����ݹ鵵״̬����
/////////////////////////////////////////////////////////////////////////
///δ�鵵����
#define THOST_FTDC_SAS_UnArchived '0'
///���ݹ鵵��
#define THOST_FTDC_SAS_Archiving '1'
///�ѹ鵵����
#define THOST_FTDC_SAS_Archived '2'
///�鵵����ʧ��
#define THOST_FTDC_SAS_ArchiveFail '3'

typedef char TThostFtdcSettArchiveStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCTPTypeType��һ��CTP����ϵͳ��������
/////////////////////////////////////////////////////////////////////////
///δ֪����
#define THOST_FTDC_CTPT_Unkown '0'
///������
#define THOST_FTDC_CTPT_MainCenter '1'
///������
#define THOST_FTDC_CTPT_BackUp '2'

typedef char TThostFtdcCTPTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcToolIDType��һ�����ߴ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcToolIDType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcToolNameType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcToolNameType[81];

/////////////////////////////////////////////////////////////////////////
///TFtdcCloseDealTypeType��һ��ƽ�ִ�����������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_CDT_Normal '0'
///Ͷ��ƽ������
#define THOST_FTDC_CDT_SpecFirst '1'

typedef char TThostFtdcCloseDealTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcMortgageFundUseRangeType��һ��������Ѻ�ʽ���÷�Χ����
/////////////////////////////////////////////////////////////////////////
///����ʹ��
#define THOST_FTDC_MFUR_None '0'
///���ڱ�֤��
#define THOST_FTDC_MFUR_Margin '1'
///���������ѡ�ӯ������֤��
#define THOST_FTDC_MFUR_All '2'
///����ҷ���3
#define THOST_FTDC_MFUR_CNY3 '3'

typedef char TThostFtdcMortgageFundUseRangeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCurrencyUnitType��һ�����ֵ�λ��������
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcCurrencyUnitType;

/////////////////////////////////////////////////////////////////////////
///TFtdcExchangeRateType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcExchangeRateType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSpecProductTypeType��һ�������Ʒ��������
/////////////////////////////////////////////////////////////////////////
///֣�����ױ���Ʒ
#define THOST_FTDC_SPT_CzceHedge '1'
///������Ѻ��Ʒ
#define THOST_FTDC_SPT_IneForeignCurrency '2'
///�������߿�ƽ�ֲ�Ʒ
#define THOST_FTDC_SPT_DceOpenClose '3'

typedef char TThostFtdcSpecProductTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFundMortgageTypeType��һ��������Ѻ��������
/////////////////////////////////////////////////////////////////////////
///��Ѻ
#define THOST_FTDC_FMT_Mortgage '1'
///����
#define THOST_FTDC_FMT_Redemption '2'

typedef char TThostFtdcFundMortgageTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAccountSettlementParamIDType��һ��Ͷ�����˻����������������
/////////////////////////////////////////////////////////////////////////
///������֤��
#define THOST_FTDC_ASPI_BaseMargin '1'
///���Ȩ���׼
#define THOST_FTDC_ASPI_LowestInterest '2'

typedef char TThostFtdcAccountSettlementParamIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCurrencyNameType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCurrencyNameType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcCurrencySignType��һ�����ַ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCurrencySignType[4];

/////////////////////////////////////////////////////////////////////////
///TFtdcFundMortDirectionType��һ��������Ѻ��������
/////////////////////////////////////////////////////////////////////////
///��������
#define THOST_FTDC_FMD_In '1'
///�����ʳ�
#define THOST_FTDC_FMD_Out '2'

typedef char TThostFtdcFundMortDirectionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBusinessClassType��һ�������������
/////////////////////////////////////////////////////////////////////////
///ӯ��
#define THOST_FTDC_BT_Profit '0'
///����
#define THOST_FTDC_BT_Loss '1'
///����
#define THOST_FTDC_BT_Other 'Z'

typedef char TThostFtdcBusinessClassType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSwapSourceTypeType��һ������������Դ����
/////////////////////////////////////////////////////////////////////////
///�ֹ�
#define THOST_FTDC_SST_Manual '0'
///�Զ�����
#define THOST_FTDC_SST_Automatic '1'

typedef char TThostFtdcSwapSourceTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCurrExDirectionType��һ��������������
/////////////////////////////////////////////////////////////////////////
///���
#define THOST_FTDC_CED_Settlement '0'
///�ۻ�
#define THOST_FTDC_CED_Sale '1'

typedef char TThostFtdcCurrExDirectionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCurrencySwapStatusType��һ������״̬����
/////////////////////////////////////////////////////////////////////////
///��¼��
#define THOST_FTDC_CSS_Entry '1'
///�����
#define THOST_FTDC_CSS_Approve '2'
///�Ѿܾ�
#define THOST_FTDC_CSS_Refuse '3'
///�ѳ���
#define THOST_FTDC_CSS_Revoke '4'
///�ѷ���
#define THOST_FTDC_CSS_Send '5'
///����ɹ�
#define THOST_FTDC_CSS_Success '6'
///����ʧ��
#define THOST_FTDC_CSS_Failure '7'

typedef char TThostFtdcCurrencySwapStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCurrExchCertNoType��һ��ƾ֤������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCurrExchCertNoType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcBatchSerialNoType��һ�����κ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBatchSerialNoType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcReqFlagType��һ�����㷢�ͱ�־����
/////////////////////////////////////////////////////////////////////////
///δ����
#define THOST_FTDC_REQF_NoSend '0'
///���ͳɹ�
#define THOST_FTDC_REQF_SendSuccess '1'
///����ʧ��
#define THOST_FTDC_REQF_SendFailed '2'
///�ȴ��ط�
#define THOST_FTDC_REQF_WaitReSend '3'

typedef char TThostFtdcReqFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcResFlagType��һ�����㷵�سɹ���־����
/////////////////////////////////////////////////////////////////////////
///�ɹ�
#define THOST_FTDC_RESF_Success '0'
///�˻�����
#define THOST_FTDC_RESF_InsuffiCient '1'
///���׽��δ֪
#define THOST_FTDC_RESF_UnKnown '8'

typedef char TThostFtdcResFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPageControlType��һ������ҳ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPageControlType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcRecordCountType��һ����¼������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcRecordCountType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCurrencySwapMemoType��һ��������ȷ����Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCurrencySwapMemoType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcExStatusType��һ���޸�״̬����
/////////////////////////////////////////////////////////////////////////
///�޸�ǰ
#define THOST_FTDC_EXS_Before '0'
///�޸ĺ�
#define THOST_FTDC_EXS_After '1'

typedef char TThostFtdcExStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcClientRegionType��һ�������ͻ���������
/////////////////////////////////////////////////////////////////////////
///���ڿͻ�
#define THOST_FTDC_CR_Domestic '1'
///�۰�̨�ͻ�
#define THOST_FTDC_CR_GMT '2'
///����ͻ�
#define THOST_FTDC_CR_Foreign '3'

typedef char TThostFtdcClientRegionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcWorkPlaceType��һ��������λ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcWorkPlaceType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcBusinessPeriodType��һ����Ӫ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBusinessPeriodType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcWebSiteType��һ����ַ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcWebSiteType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcUOAIdCardTypeType��һ��ͳһ����֤����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUOAIdCardTypeType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcClientModeType��һ������ģʽ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcClientModeType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcInvestorFullNameType��һ��Ͷ����ȫ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInvestorFullNameType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcUOABrokerIDType��һ�������н����ID����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUOABrokerIDType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcUOAZipCodeType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUOAZipCodeType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcUOAEMailType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUOAEMailType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcOldCityType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOldCityType[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcCorporateIdentifiedCardNoType��һ�����˴���֤����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCorporateIdentifiedCardNoType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcHasBoardType��һ���Ƿ��ж��»�����
/////////////////////////////////////////////////////////////////////////
///û��
#define THOST_FTDC_HB_No '0'
///��
#define THOST_FTDC_HB_Yes '1'

typedef char TThostFtdcHasBoardType;

/////////////////////////////////////////////////////////////////////////
///TFtdcStartModeType��һ������ģʽ����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_SM_Normal '1'
///Ӧ��
#define THOST_FTDC_SM_Emerge '2'
///�ָ�
#define THOST_FTDC_SM_Restore '3'

typedef char TThostFtdcStartModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTemplateTypeType��һ��ģ����������
/////////////////////////////////////////////////////////////////////////
///ȫ��
#define THOST_FTDC_TPT_Full '1'
///����
#define THOST_FTDC_TPT_Increment '2'
///����
#define THOST_FTDC_TPT_BackUp '3'

typedef char TThostFtdcTemplateTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcLoginModeType��һ����¼ģʽ����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_LM_Trade '0'
///ת��
#define THOST_FTDC_LM_Transfer '1'

typedef char TThostFtdcLoginModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcPromptTypeType��һ��������ʾ��������
/////////////////////////////////////////////////////////////////////////
///��Լ������
#define THOST_FTDC_CPT_Instrument '1'
///��֤��ֶ���Ч
#define THOST_FTDC_CPT_Margin '2'

typedef char TThostFtdcPromptTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcLedgerManageIDType��һ���ֻ������ʲ���������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcLedgerManageIDType[51];

/////////////////////////////////////////////////////////////////////////
///TFtdcInvestVarietyType��һ��Ͷ��Ʒ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInvestVarietyType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcBankAccountTypeType��һ���˻��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBankAccountTypeType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcLedgerManageBankType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcLedgerManageBankType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcCffexDepartmentNameType��һ������Ӫҵ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCffexDepartmentNameType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcCffexDepartmentCodeType��һ��Ӫҵ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCffexDepartmentCodeType[9];

/////////////////////////////////////////////////////////////////////////
///TFtdcHasTrusteeType��һ���Ƿ����й�������
/////////////////////////////////////////////////////////////////////////
///��
#define THOST_FTDC_HT_Yes '1'
///û��
#define THOST_FTDC_HT_No '0'

typedef char TThostFtdcHasTrusteeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCMemo1Type��һ��˵������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCMemo1Type[41];

/////////////////////////////////////////////////////////////////////////
///TFtdcAssetmgrCFullNameType��һ�������ʲ�����ҵ����ڻ���˾ȫ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAssetmgrCFullNameType[101];

/////////////////////////////////////////////////////////////////////////
///TFtdcAssetmgrApprovalNOType��һ���ʲ�����ҵ�����ĺ�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAssetmgrApprovalNOType[51];

/////////////////////////////////////////////////////////////////////////
///TFtdcAssetmgrMgrNameType��һ���ʲ�����ҵ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAssetmgrMgrNameType[401];

/////////////////////////////////////////////////////////////////////////
///TFtdcAmTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_AMT_Bank '1'
///֤ȯ��˾
#define THOST_FTDC_AMT_Securities '2'
///����˾
#define THOST_FTDC_AMT_Fund '3'
///���չ�˾
#define THOST_FTDC_AMT_Insurance '4'
///���й�˾
#define THOST_FTDC_AMT_Trust '5'
///����
#define THOST_FTDC_AMT_Other '9'

typedef char TThostFtdcAmTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCAmTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCAmTypeType[5];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCFundIOTypeType��һ���������������
/////////////////////////////////////////////////////////////////////////
///�����
#define THOST_FTDC_CFIOT_FundIO '0'
///���ڻ���
#define THOST_FTDC_CFIOT_SwapCurrency '1'

typedef char TThostFtdcCSRCFundIOTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCusAccountTypeType��һ�������˻���������
/////////////////////////////////////////////////////////////////////////
///�ڻ������˻�
#define THOST_FTDC_CAT_Futures '1'
///���ڻ��ʹ�ҵ���µ��ʹܽ����˻�
#define THOST_FTDC_CAT_AssetmgrFuture '2'
///�ۺ����ʹ�ҵ���µ��ڻ��ʹ��й��˻�
#define THOST_FTDC_CAT_AssetmgrTrustee '3'
///�ۺ����ʹ�ҵ���µ��ʽ���ת�˻�
#define THOST_FTDC_CAT_AssetmgrTransfer '4'

typedef char TThostFtdcCusAccountTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCNationalType��һ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCNationalType[4];

/////////////////////////////////////////////////////////////////////////
///TFtdcCSRCSecAgentIDType��һ����������ID����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCSRCSecAgentIDType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcLanguageTypeType��һ��֪ͨ������������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_LT_Chinese '1'
///Ӣ��
#define THOST_FTDC_LT_English '2'

typedef char TThostFtdcLanguageTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAmAccountType��һ��Ͷ���˻�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAmAccountType[23];

/////////////////////////////////////////////////////////////////////////
///TFtdcAssetmgrClientTypeType��һ���ʲ�����ͻ���������
/////////////////////////////////////////////////////////////////////////
///�����ʹܿͻ�
#define THOST_FTDC_AMCT_Person '1'
///��λ�ʹܿͻ�
#define THOST_FTDC_AMCT_Organ '2'
///���ⵥλ�ʹܿͻ�
#define THOST_FTDC_AMCT_SpecialOrgan '4'

typedef char TThostFtdcAssetmgrClientTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAssetmgrTypeType��һ��Ͷ����������
/////////////////////////////////////////////////////////////////////////
///�ڻ���
#define THOST_FTDC_ASST_Futures '3'
///�ۺ���
#define THOST_FTDC_ASST_SpecialOrgan '4'

typedef char TThostFtdcAssetmgrTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcUOMType��һ��������λ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcUOMType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcSHFEInstLifePhaseType��һ����������Լ��������״̬����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSHFEInstLifePhaseType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcSHFEProductClassType��һ����Ʒ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSHFEProductClassType[11];

/////////////////////////////////////////////////////////////////////////
///TFtdcPriceDecimalType��һ���۸�С��λ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcPriceDecimalType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcInTheMoneyFlagType��һ��ƽֵ��Ȩ��־����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInTheMoneyFlagType[2];

/////////////////////////////////////////////////////////////////////////
///TFtdcCheckInstrTypeType��һ����Լ�Ƚ���������
/////////////////////////////////////////////////////////////////////////
///��Լ������������
#define THOST_FTDC_CIT_HasExch '0'
///��Լ��ϵͳ������
#define THOST_FTDC_CIT_HasATP '1'
///��Լ�Ƚϲ�һ��
#define THOST_FTDC_CIT_HasDiff '2'

typedef char TThostFtdcCheckInstrTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcDeliveryTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
///�ֹ�����
#define THOST_FTDC_DT_HandDeliv '1'
///���ڽ���
#define THOST_FTDC_DT_PersonDeliv '2'

typedef char TThostFtdcDeliveryTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBigMoneyType��һ���ʽ�����
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcBigMoneyType;

/////////////////////////////////////////////////////////////////////////
///TFtdcMaxMarginSideAlgorithmType��һ�����߱�֤���㷨����
/////////////////////////////////////////////////////////////////////////
///��ʹ�ô��߱�֤���㷨
#define THOST_FTDC_MMSA_NO '0'
///ʹ�ô��߱�֤���㷨
#define THOST_FTDC_MMSA_YES '1'

typedef char TThostFtdcMaxMarginSideAlgorithmType;

/////////////////////////////////////////////////////////////////////////
///TFtdcDAClientTypeType��һ���ʲ�����ͻ���������
/////////////////////////////////////////////////////////////////////////
///��Ȼ��
#define THOST_FTDC_CACT_Person '0'
///����
#define THOST_FTDC_CACT_Company '1'
///����
#define THOST_FTDC_CACT_Other '2'

typedef char TThostFtdcDAClientTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCombinInstrIDType��һ��������Լ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCombinInstrIDType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcCombinSettlePriceType��һ�����Ƚ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCombinSettlePriceType[61];

/////////////////////////////////////////////////////////////////////////
///TFtdcDCEPriorityType��һ�����ȼ�����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcDCEPriorityType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTradeGroupIDType��һ���ɽ��������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcTradeGroupIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcIsCheckPrepaType��һ���Ƿ�У�鿪�������ʽ�����
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcIsCheckPrepaType;

/////////////////////////////////////////////////////////////////////////
///TFtdcUOAAssetmgrTypeType��һ��Ͷ����������
/////////////////////////////////////////////////////////////////////////
///�ڻ���
#define THOST_FTDC_UOAAT_Futures '1'
///�ۺ���
#define THOST_FTDC_UOAAT_SpecialOrgan '2'

typedef char TThostFtdcUOAAssetmgrTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcDirectionEnType��һ��������������
/////////////////////////////////////////////////////////////////////////
///Buy
#define THOST_FTDC_DEN_Buy '0'
///Sell
#define THOST_FTDC_DEN_Sell '1'

typedef char TThostFtdcDirectionEnType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOffsetFlagEnType��һ����ƽ��־����
/////////////////////////////////////////////////////////////////////////
///Position Opening
#define THOST_FTDC_OFEN_Open '0'
///Position Close
#define THOST_FTDC_OFEN_Close '1'
///Forced Liquidation
#define THOST_FTDC_OFEN_ForceClose '2'
///Close Today
#define THOST_FTDC_OFEN_CloseToday '3'
///Close Prev.
#define THOST_FTDC_OFEN_CloseYesterday '4'
///Forced Reduction
#define THOST_FTDC_OFEN_ForceOff '5'
///Local Forced Liquidation
#define THOST_FTDC_OFEN_LocalForceClose '6'

typedef char TThostFtdcOffsetFlagEnType;

/////////////////////////////////////////////////////////////////////////
///TFtdcHedgeFlagEnType��һ��Ͷ���ױ���־����
/////////////////////////////////////////////////////////////////////////
///Speculation
#define THOST_FTDC_HFEN_Speculation '1'
///Arbitrage
#define THOST_FTDC_HFEN_Arbitrage '2'
///Hedge
#define THOST_FTDC_HFEN_Hedge '3'

typedef char TThostFtdcHedgeFlagEnType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFundIOTypeEnType��һ���������������
/////////////////////////////////////////////////////////////////////////
///Deposit/Withdrawal
#define THOST_FTDC_FIOTEN_FundIO '1'
///Bank-Futures Transfer
#define THOST_FTDC_FIOTEN_Transfer '2'
///Bank-Futures FX Exchange
#define THOST_FTDC_FIOTEN_SwapCurrency '3'

typedef char TThostFtdcFundIOTypeEnType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFundTypeEnType��һ���ʽ���������
/////////////////////////////////////////////////////////////////////////
///Bank Deposit
#define THOST_FTDC_FTEN_Deposite '1'
///Payment/Fee
#define THOST_FTDC_FTEN_ItemFund '2'
///Brokerage Adj
#define THOST_FTDC_FTEN_Company '3'
///Internal Transfer
#define THOST_FTDC_FTEN_InnerTransfer '4'

typedef char TThostFtdcFundTypeEnType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFundDirectionEnType��һ�������������
/////////////////////////////////////////////////////////////////////////
///Deposit
#define THOST_FTDC_FDEN_In '1'
///Withdrawal
#define THOST_FTDC_FDEN_Out '2'

typedef char TThostFtdcFundDirectionEnType;

/////////////////////////////////////////////////////////////////////////
///TFtdcFundMortDirectionEnType��һ��������Ѻ��������
/////////////////////////////////////////////////////////////////////////
///Pledge
#define THOST_FTDC_FMDEN_In '1'
///Redemption
#define THOST_FTDC_FMDEN_Out '2'

typedef char TThostFtdcFundMortDirectionEnType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSwapBusinessTypeType��һ������ҵ����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSwapBusinessTypeType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcOptionsTypeType��һ����Ȩ��������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_CP_CallOptions '1'
///����
#define THOST_FTDC_CP_PutOptions '2'

typedef char TThostFtdcOptionsTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcStrikeModeType��һ��ִ�з�ʽ����
/////////////////////////////////////////////////////////////////////////
///ŷʽ
#define THOST_FTDC_STM_Continental '0'
///��ʽ
#define THOST_FTDC_STM_American '1'
///��Ľ��
#define THOST_FTDC_STM_Bermuda '2'

typedef char TThostFtdcStrikeModeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcStrikeTypeType��һ��ִ����������
/////////////////////////////////////////////////////////////////////////
///����Գ�
#define THOST_FTDC_STT_Hedge '0'
///ƥ��ִ��
#define THOST_FTDC_STT_Match '1'

typedef char TThostFtdcStrikeTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcApplyTypeType��һ���н�����Ȩ����ִ��������������
/////////////////////////////////////////////////////////////////////////
///��ִ������
#define THOST_FTDC_APPT_NotStrikeNum '4'

typedef char TThostFtdcApplyTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcGiveUpDataSourceType��һ������ִ������������Դ����
/////////////////////////////////////////////////////////////////////////
///ϵͳ����
#define THOST_FTDC_GUDS_Gen '0'
///�ֹ����
#define THOST_FTDC_GUDS_Hand '1'

typedef char TThostFtdcGiveUpDataSourceType;

/////////////////////////////////////////////////////////////////////////
///TFtdcExecOrderSysIDType��һ��ִ������ϵͳ�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcExecOrderSysIDType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcExecResultType��һ��ִ�н������
/////////////////////////////////////////////////////////////////////////
///û��ִ��
#define THOST_FTDC_OER_NoExec 'n'
///�Ѿ�ȡ��
#define THOST_FTDC_OER_Canceled 'c'
///ִ�гɹ�
#define THOST_FTDC_OER_OK '0'
///��Ȩ�ֲֲ���
#define THOST_FTDC_OER_NoPosition '1'
///�ʽ𲻹�
#define THOST_FTDC_OER_NoDeposit '2'
///��Ա������
#define THOST_FTDC_OER_NoParticipant '3'
///�ͻ�������
#define THOST_FTDC_OER_NoClient '4'
///��Լ������
#define THOST_FTDC_OER_NoInstrument '6'
///û��ִ��Ȩ��
#define THOST_FTDC_OER_NoRight '7'
///�����������
#define THOST_FTDC_OER_InvalidVolume '8'
///û���㹻����ʷ�ɽ�
#define THOST_FTDC_OER_NoEnoughHistoryTrade '9'
///δ֪
#define THOST_FTDC_OER_Unknown 'a'

typedef char TThostFtdcExecResultType;

/////////////////////////////////////////////////////////////////////////
///TFtdcStrikeSequenceType��һ��ִ���������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcStrikeSequenceType;

/////////////////////////////////////////////////////////////////////////
///TFtdcStrikeTimeType��һ��ִ��ʱ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcStrikeTimeType[13];

/////////////////////////////////////////////////////////////////////////
///TFtdcCombinationTypeType��һ�������������
/////////////////////////////////////////////////////////////////////////
///�ڻ����
#define THOST_FTDC_COMBT_Future '0'
///��ֱ�۲�BUL
#define THOST_FTDC_COMBT_BUL '1'
///��ֱ�۲�BER
#define THOST_FTDC_COMBT_BER '2'
///��ʽ���
#define THOST_FTDC_COMBT_STD '3'
///���ʽ���
#define THOST_FTDC_COMBT_STG '4'
///�������
#define THOST_FTDC_COMBT_PRT '5'
///ʱ��۲����
#define THOST_FTDC_COMBT_CAS '6'
///��Ȩ�������
#define THOST_FTDC_COMBT_OPL '7'
///�򱸶����
#define THOST_FTDC_COMBT_BFO '8'
///������Ȩ��ֱ�۲����
#define THOST_FTDC_COMBT_BLS '9'
///������Ȩ��ֱ�۲����
#define THOST_FTDC_COMBT_BES 'a'

typedef char TThostFtdcCombinationTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcDceCombinationTypeType��һ�������������
/////////////////////////////////////////////////////////////////////////
///�ڻ��������
#define THOST_FTDC_DCECOMBT_SPL '0'
///��Ȩ�������
#define THOST_FTDC_DCECOMBT_OPL '1'
///�ڻ��������
#define THOST_FTDC_DCECOMBT_SP '2'
///�ڻ���Ʒ�����
#define THOST_FTDC_DCECOMBT_SPC '3'
///������Ȩ��ֱ�۲����
#define THOST_FTDC_DCECOMBT_BLS '4'
///������Ȩ��ֱ�۲����
#define THOST_FTDC_DCECOMBT_BES '5'
///��Ȩ�����۲����
#define THOST_FTDC_DCECOMBT_CAS '6'
///��Ȩ��ʽ���
#define THOST_FTDC_DCECOMBT_STD '7'
///��Ȩ���ʽ���
#define THOST_FTDC_DCECOMBT_STG '8'
///�����ڻ���Ȩ���
#define THOST_FTDC_DCECOMBT_BFO '9'
///�����ڻ���Ȩ���
#define THOST_FTDC_DCECOMBT_SFO 'a'

typedef char TThostFtdcDceCombinationTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOptionRoyaltyPriceTypeType��һ����ȨȨ����۸���������
/////////////////////////////////////////////////////////////////////////
///������
#define THOST_FTDC_ORPT_PreSettlementPrice '1'
///���ּ�
#define THOST_FTDC_ORPT_OpenPrice '4'
///���¼��������۽ϴ�ֵ
#define THOST_FTDC_ORPT_MaxPreSettlementPrice '5'

typedef char TThostFtdcOptionRoyaltyPriceTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBalanceAlgorithmType��һ��Ȩ���㷨����
/////////////////////////////////////////////////////////////////////////
///��������Ȩ��ֵӯ��
#define THOST_FTDC_BLAG_Default '1'
///������Ȩ��ֵ����
#define THOST_FTDC_BLAG_IncludeOptValLost '2'

typedef char TThostFtdcBalanceAlgorithmType;

/////////////////////////////////////////////////////////////////////////
///TFtdcActionTypeType��һ��ִ����������
/////////////////////////////////////////////////////////////////////////
///ִ��
#define THOST_FTDC_ACTP_Exec '1'
///����
#define THOST_FTDC_ACTP_Abandon '2'

typedef char TThostFtdcActionTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcForQuoteStatusType��һ��ѯ��״̬����
/////////////////////////////////////////////////////////////////////////
///�Ѿ��ύ
#define THOST_FTDC_FQST_Submitted 'a'
///�Ѿ�����
#define THOST_FTDC_FQST_Accepted 'b'
///�Ѿ����ܾ�
#define THOST_FTDC_FQST_Rejected 'c'

typedef char TThostFtdcForQuoteStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcValueMethodType��һ��ȡֵ��ʽ����
/////////////////////////////////////////////////////////////////////////
///������ֵ
#define THOST_FTDC_VM_Absolute '0'
///������
#define THOST_FTDC_VM_Ratio '1'

typedef char TThostFtdcValueMethodType;

/////////////////////////////////////////////////////////////////////////
///TFtdcExecOrderPositionFlagType��һ����Ȩ��Ȩ���Ƿ����ڻ�ͷ��ı������
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_EOPF_Reserve '0'
///������
#define THOST_FTDC_EOPF_UnReserve '1'

typedef char TThostFtdcExecOrderPositionFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcExecOrderCloseFlagType��һ����Ȩ��Ȩ�����ɵ�ͷ���Ƿ��Զ�ƽ������
/////////////////////////////////////////////////////////////////////////
///�Զ�ƽ��
#define THOST_FTDC_EOCF_AutoClose '0'
///�����Զ�ƽ��
#define THOST_FTDC_EOCF_NotToClose '1'

typedef char TThostFtdcExecOrderCloseFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcProductTypeType��һ����Ʒ��������
/////////////////////////////////////////////////////////////////////////
///�ڻ�
#define THOST_FTDC_PTE_Futures '1'
///��Ȩ
#define THOST_FTDC_PTE_Options '2'

typedef char TThostFtdcProductTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCZCEUploadFileNameType��һ��֣���������ļ�������
/////////////////////////////////////////////////////////////////////////
///^\d{8}_zz_\d{4}
#define THOST_FTDC_CUFN_CUFN_O 'O'
///^\d{8}�ɽ���
#define THOST_FTDC_CUFN_CUFN_T 'T'
///^\d{8}���ȳֱֲ�new
#define THOST_FTDC_CUFN_CUFN_P 'P'
///^\d{8}��ƽ���˽��
#define THOST_FTDC_CUFN_CUFN_N 'N'
///^\d{8}ƽ�ֱ�
#define THOST_FTDC_CUFN_CUFN_L 'L'
///^\d{8}�ʽ��
#define THOST_FTDC_CUFN_CUFN_F 'F'
///^\d{8}��ϳֱֲ�
#define THOST_FTDC_CUFN_CUFN_C 'C'
///^\d{8}��֤�������
#define THOST_FTDC_CUFN_CUFN_M 'M'

typedef char TThostFtdcCZCEUploadFileNameType;

/////////////////////////////////////////////////////////////////////////
///TFtdcDCEUploadFileNameType��һ�������������ļ�������
/////////////////////////////////////////////////////////////////////////
///^\d{8}_dl_\d{3}
#define THOST_FTDC_DUFN_DUFN_O 'O'
///^\d{8}_�ɽ���
#define THOST_FTDC_DUFN_DUFN_T 'T'
///^\d{8}_�ֱֲ�
#define THOST_FTDC_DUFN_DUFN_P 'P'
///^\d{8}_�ʽ�����
#define THOST_FTDC_DUFN_DUFN_F 'F'
///^\d{8}_�Ż���ϳֲ���ϸ��
#define THOST_FTDC_DUFN_DUFN_C 'C'
///^\d{8}_�ֲ���ϸ��
#define THOST_FTDC_DUFN_DUFN_D 'D'
///^\d{8}_��֤�������
#define THOST_FTDC_DUFN_DUFN_M 'M'
///^\d{8}_��Ȩִ�б�
#define THOST_FTDC_DUFN_DUFN_S 'S'

typedef char TThostFtdcDCEUploadFileNameType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSHFEUploadFileNameType��һ�������������ļ�������
/////////////////////////////////////////////////////////////////////////
///^\d{4}_\d{8}_\d{8}_DailyFundChg
#define THOST_FTDC_SUFN_SUFN_O 'O'
///^\d{4}_\d{8}_\d{8}_Trade
#define THOST_FTDC_SUFN_SUFN_T 'T'
///^\d{4}_\d{8}_\d{8}_SettlementDetail
#define THOST_FTDC_SUFN_SUFN_P 'P'
///^\d{4}_\d{8}_\d{8}_Capital
#define THOST_FTDC_SUFN_SUFN_F 'F'

typedef char TThostFtdcSHFEUploadFileNameType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCFFEXUploadFileNameType��һ���н��������ļ�������
/////////////////////////////////////////////////////////////////////////
///^\d{4}_SG\d{1}_\d{8}_\d{1}_Trade
#define THOST_FTDC_CFUFN_SUFN_T 'T'
///^\d{4}_SG\d{1}_\d{8}_\d{1}_SettlementDetail
#define THOST_FTDC_CFUFN_SUFN_P 'P'
///^\d{4}_SG\d{1}_\d{8}_\d{1}_Capital
#define THOST_FTDC_CFUFN_SUFN_F 'F'
///^\d{4}_SG\d{1}_\d{8}_\d{1}_OptionExec
#define THOST_FTDC_CFUFN_SUFN_S 'S'

typedef char TThostFtdcCFFEXUploadFileNameType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCombDirectionType��һ�����ָ�������
/////////////////////////////////////////////////////////////////////////
///�������
#define THOST_FTDC_CMDR_Comb '0'
///������
#define THOST_FTDC_CMDR_UnComb '1'
///����Աɾ��ϵ�
#define THOST_FTDC_CMDR_DelComb '2'

typedef char TThostFtdcCombDirectionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcStrikeOffsetTypeType��һ����Ȩƫ����������
/////////////////////////////////////////////////////////////////////////
///ʵֵ��
#define THOST_FTDC_STOV_RealValue '1'
///ӯ����
#define THOST_FTDC_STOV_ProfitValue '2'
///ʵֵ����
#define THOST_FTDC_STOV_RealRatio '3'
///ӯ������
#define THOST_FTDC_STOV_ProfitRatio '4'

typedef char TThostFtdcStrikeOffsetTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcReserveOpenAccStasType��һ��ԤԼ����״̬����
/////////////////////////////////////////////////////////////////////////
///�ȴ�������
#define THOST_FTDC_ROAST_Processing '0'
///�ѳ���
#define THOST_FTDC_ROAST_Cancelled '1'
///�ѿ���
#define THOST_FTDC_ROAST_Opened '2'
///��Ч����
#define THOST_FTDC_ROAST_Invalid '3'

typedef char TThostFtdcReserveOpenAccStasType;

/////////////////////////////////////////////////////////////////////////
///TFtdcLoginRemarkType��һ����¼��ע����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcLoginRemarkType[36];

/////////////////////////////////////////////////////////////////////////
///TFtdcInvestUnitIDType��һ��Ͷ�ʵ�Ԫ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcInvestUnitIDType[17];

/////////////////////////////////////////////////////////////////////////
///TFtdcBulletinIDType��һ������������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcBulletinIDType;

/////////////////////////////////////////////////////////////////////////
///TFtdcNewsTypeType��һ��������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcNewsTypeType[3];

/////////////////////////////////////////////////////////////////////////
///TFtdcNewsUrgencyType��һ�������̶�����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcNewsUrgencyType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAbstractType��һ����ϢժҪ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAbstractType[81];

/////////////////////////////////////////////////////////////////////////
///TFtdcComeFromType��һ����Ϣ��Դ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcComeFromType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcURLLinkType��һ��WEB��ַ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcURLLinkType[201];

/////////////////////////////////////////////////////////////////////////
///TFtdcLongIndividualNameType��һ����������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcLongIndividualNameType[161];

/////////////////////////////////////////////////////////////////////////
///TFtdcLongFBEBankAccountNameType��һ�������������˻�������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcLongFBEBankAccountNameType[161];

/////////////////////////////////////////////////////////////////////////
///TFtdcDateTimeType��һ������ʱ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcDateTimeType[17];

/////////////////////////////////////////////////////////////////////////
///TFtdcWeakPasswordSourceType��һ����������Դ����
/////////////////////////////////////////////////////////////////////////
///�������
#define THOST_FTDC_WPSR_Lib '1'
///�ֹ�¼��
#define THOST_FTDC_WPSR_Manual '2'

typedef char TThostFtdcWeakPasswordSourceType;

/////////////////////////////////////////////////////////////////////////
///TFtdcRandomStringType��һ�����������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcRandomStringType[17];

/////////////////////////////////////////////////////////////////////////
///TFtdcOptSelfCloseFlagType��һ����Ȩ��Ȩ��ͷ���Ƿ��ԶԳ�����
/////////////////////////////////////////////////////////////////////////
///�ԶԳ���Ȩ��λ
#define THOST_FTDC_OSCF_CloseSelfOptionPosition '1'
///������Ȩ��λ
#define THOST_FTDC_OSCF_ReserveOptionPosition '2'
///�ԶԳ�������Լ����ڻ���λ
#define THOST_FTDC_OSCF_SellCloseSelfFuturePosition '3'
///����������Լ����ڻ���λ
#define THOST_FTDC_OSCF_ReserveFuturePosition '4'

typedef char TThostFtdcOptSelfCloseFlagType;

/////////////////////////////////////////////////////////////////////////
///TFtdcBizTypeType��һ��ҵ����������
/////////////////////////////////////////////////////////////////////////
///�ڻ�
#define THOST_FTDC_BZTP_Future '1'
///֤ȯ
#define THOST_FTDC_BZTP_Stock '2'

typedef char TThostFtdcBizTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAppTypeType��һ���û�App��������
/////////////////////////////////////////////////////////////////////////
///ֱ����Ͷ����
#define THOST_FTDC_APP_TYPE_Investor '1'
///Ϊÿ��Ͷ���߶��������ӵ��м�
#define THOST_FTDC_APP_TYPE_InvestorRelay '2'
///����Ͷ���߹���һ������Ա���ӵ��м�
#define THOST_FTDC_APP_TYPE_OperatorRelay '3'
///δ֪
#define THOST_FTDC_APP_TYPE_UnKnown '4'

typedef char TThostFtdcAppTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAppIDType��һ��App��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAppIDType[33];

/////////////////////////////////////////////////////////////////////////
///TFtdcSystemInfoLenType��һ��ϵͳ��Ϣ��������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcSystemInfoLenType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAdditionalInfoLenType��һ��������Ϣ��������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcAdditionalInfoLenType;

/////////////////////////////////////////////////////////////////////////
///TFtdcClientSystemInfoType��һ�������ն�ϵͳ��Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcClientSystemInfoType[273];

/////////////////////////////////////////////////////////////////////////
///TFtdcAdditionalInfoType��һ��ϵͳ�ⲿ��Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcAdditionalInfoType[261];

/////////////////////////////////////////////////////////////////////////
///TFtdcBase64ClientSystemInfoType��һ��base64�����ն�ϵͳ��Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBase64ClientSystemInfoType[365];

/////////////////////////////////////////////////////////////////////////
///TFtdcBase64AdditionalInfoType��һ��base64ϵͳ�ⲿ��Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcBase64AdditionalInfoType[349];

/////////////////////////////////////////////////////////////////////////
///TFtdcCurrentAuthMethodType��һ����ǰ���õ���֤ģʽ��0����������֤ģʽ A�ӵ�λ��ʼ���һλ����ͼƬ��֤�룬�����ڶ�λ����̬�����������λ���������֤������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcCurrentAuthMethodType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCaptchaInfoLenType��һ��ͼƬ��֤��Ϣ��������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcCaptchaInfoLenType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCaptchaInfoType��һ��ͼƬ��֤��Ϣ����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCaptchaInfoType[2561];

/////////////////////////////////////////////////////////////////////////
///TFtdcUserTextSeqType��һ���û�������֤��ı������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcUserTextSeqType;

/////////////////////////////////////////////////////////////////////////
///TFtdcHandshakeDataType��һ������������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcHandshakeDataType[301];

/////////////////////////////////////////////////////////////////////////
///TFtdcHandshakeDataLenType��һ�������������ݳ�������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcHandshakeDataLenType;

/////////////////////////////////////////////////////////////////////////
///TFtdcCryptoKeyVersionType��һ��api��frontͨ����Կ�汾������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCryptoKeyVersionType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcRsaKeyVersionType��һ����Կ�汾������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcRsaKeyVersionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSoftwareProviderIDType��һ�����������ID����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSoftwareProviderIDType[22];

/////////////////////////////////////////////////////////////////////////
///TFtdcCollectTimeType��һ����Ϣ�ɼ�ʱ������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcCollectTimeType[21];

/////////////////////////////////////////////////////////////////////////
///TFtdcQueryFreqType��һ����ѯƵ������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcQueryFreqType;

/////////////////////////////////////////////////////////////////////////
///TFtdcResponseValueType��һ��Ӧ����������
/////////////////////////////////////////////////////////////////////////
///���ɹ�
#define THOST_FTDC_RV_Right '0'
///���ʧ��
#define THOST_FTDC_RV_Refuse '1'

typedef char TThostFtdcResponseValueType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOTCTradeTypeType��һ��OTC�ɽ���������
/////////////////////////////////////////////////////////////////////////
///���ڽ���
#define THOST_FTDC_OTC_TRDT_Block '0'
///��ת��
#define THOST_FTDC_OTC_TRDT_EFP '1'

typedef char TThostFtdcOTCTradeTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcMatchTypeType��һ�����ַ���ƥ�䷽ʽ����
/////////////////////////////////////////////////////////////////////////
///�����ֵ
#define THOST_FTDC_OTC_MT_DV01 '1'
///��ֵ
#define THOST_FTDC_OTC_MT_ParValue '2'

typedef char TThostFtdcMatchTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOTCTraderIDType��һ��OTC����Ա��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcOTCTraderIDType[31];

/////////////////////////////////////////////////////////////////////////
///TFtdcRiskValueType��һ���ڻ�����ֵ����
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcRiskValueType;

/////////////////////////////////////////////////////////////////////////
///TFtdcIDBNameType��һ������������������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcIDBNameType[100];

/////////////////////////////////////////////////////////////////////////
///TFtdcDiscountRatioType��һ���ۿ�������
/////////////////////////////////////////////////////////////////////////
typedef double TThostFtdcDiscountRatioType;

/////////////////////////////////////////////////////////////////////////
///TFtdcAuthTypeType��һ���û��ն���֤��ʽ����
/////////////////////////////////////////////////////////////////////////
///������У��
#define THOST_FTDC_AU_WHITE '0'
///������У��
#define THOST_FTDC_AU_BLACK '1'

typedef char TThostFtdcAuthTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcClassTypeType��һ����Լ���෽ʽ����
/////////////////////////////////////////////////////////////////////////
///���к�Լ
#define THOST_FTDC_INS_ALL '0'
///�ڻ������ڡ���ת�֡�Tas������ָ����Լ
#define THOST_FTDC_INS_FUTURE '1'
///�ڻ����ֻ���Ȩ��Լ
#define THOST_FTDC_INS_OPTION '2'
///��Ϻ�Լ
#define THOST_FTDC_INS_COMB '3'

typedef char TThostFtdcClassTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcTradingTypeType��һ����Լ����״̬���෽ʽ����
/////////////////////////////////////////////////////////////////////////
///����״̬
#define THOST_FTDC_TD_ALL '0'
///����
#define THOST_FTDC_TD_TRADE '1'
///�ǽ���
#define THOST_FTDC_TD_UNTRADE '2'

typedef char TThostFtdcTradingTypeType;

/////////////////////////////////////////////////////////////////////////
///TFtdcProductStatusType��һ����Ʒ״̬����
/////////////////////////////////////////////////////////////////////////
///�ɽ���
#define THOST_FTDC_PS_tradeable '1'
///���ɽ���
#define THOST_FTDC_PS_untradeable '2'

typedef char TThostFtdcProductStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSyncDeltaStatusType��һ��׷ƽ״̬����
/////////////////////////////////////////////////////////////////////////
///���׿ɶ�
#define THOST_FTDC_SDS_Readable '1'
///�����ڶ�
#define THOST_FTDC_SDS_Reading '2'
///���׶�ȡ���
#define THOST_FTDC_SDS_Readend '3'
///׷ƽʧ�� ���ױ���״̬���㲻����
#define THOST_FTDC_SDS_OptErr 'e'

typedef char TThostFtdcSyncDeltaStatusType;

/////////////////////////////////////////////////////////////////////////
///TFtdcActionDirectionType��һ��������־����
/////////////////////////////////////////////////////////////////////////
///����
#define THOST_FTDC_ACD_Add '1'
///ɾ��
#define THOST_FTDC_ACD_Del '2'
///����
#define THOST_FTDC_ACD_Upd '3'

typedef char TThostFtdcActionDirectionType;

/////////////////////////////////////////////////////////////////////////
///TFtdcOrderCancelAlgType��һ������ʱѡ��ϯλ�㷨����
/////////////////////////////////////////////////////////////////////////
///��ѯϯλ����
#define THOST_FTDC_OAC_Balance '1'
///����ԭ����ϯλ����
#define THOST_FTDC_OAC_OrigFirst '2'

typedef char TThostFtdcOrderCancelAlgType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSyncDescriptionType��һ��׷ƽ��������
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSyncDescriptionType[257];

/////////////////////////////////////////////////////////////////////////
///TFtdcCommonIntType��һ��ͨ��int��������
/////////////////////////////////////////////////////////////////////////
typedef int TThostFtdcCommonIntType;

/////////////////////////////////////////////////////////////////////////
///TFtdcSysVersionType��һ��ϵͳ�汾����
/////////////////////////////////////////////////////////////////////////
typedef char TThostFtdcSysVersionType[41];

#endif
